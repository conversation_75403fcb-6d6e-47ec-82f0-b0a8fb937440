#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 DIAGNOSTIC COMPLET DE L'ÉVALUATION DE PERFORMANCE
==================================================
Identifie exactement pourquoi l'évaluation retourne 0 prédictions
"""

import json
from analyseur_fractal_baccarat import AnalyseurFractalBaccarat

def diagnostic_complet():
    """
    🔍 DIAGNOSTIC ÉTAPE PAR ÉTAPE
    """
    print("🔍 DIAGNOSTIC COMPLET DE L'ÉVALUATION DE PERFORMANCE")
    print("=" * 60)
    
    # Initialiser l'analyseur
    analyseur = AnalyseurFractalBaccarat()
    
    # Charger le dataset
    print("📂 Chargement du dataset...")
    dataset = analyseur.charger_dataset()
    parties = dataset.get('parties', [])
    
    print(f"✅ Dataset chargé: {len(parties)} parties")
    
    # Paramètres d'évaluation
    pourcentage_test = 0.3
    nb_parties_total = len(parties)
    nb_parties_test = int(nb_parties_total * pourcentage_test)
    parties_test = parties[-nb_parties_test:]
    
    print(f"📊 Parties de test: {len(parties_test)} (30% des {nb_parties_total})")
    
    # Compteurs de diagnostic
    parties_analysees = 0
    parties_trop_courtes = 0
    mains_analysees = 0
    mains_sans_resultat = 0
    mains_tie = 0
    predictions_indeterminees = 0
    predictions_valides = 0
    erreurs_analyse = 0
    
    print(f"\n🔬 ANALYSE DÉTAILLÉE DES PARTIES DE TEST...")
    
    # Analyser les premières parties pour diagnostic
    for idx, partie in enumerate(parties_test[:5]):  # Limiter à 5 parties pour diagnostic
        print(f"\n--- PARTIE {idx+1} ---")
        mains = partie.get('mains', [])
        print(f"Nombre de mains: {len(mains)}")
        
        if len(mains) < 10:
            print("❌ Partie trop courte (< 10 mains)")
            parties_trop_courtes += 1
            continue
            
        parties_analysees += 1
        
        # Analyser quelques mains de cette partie
        for i in range(10, min(15, len(mains))):  # Analyser 5 mains max par partie
            mains_analyse = mains[:i]
            main_cible = mains[i]
            resultat_reel = main_cible.get('index3_result')
            
            mains_analysees += 1
            print(f"  Main {i}: {resultat_reel}")
            
            if not resultat_reel:
                print("    ❌ Pas de résultat")
                mains_sans_resultat += 1
                continue
                
            if resultat_reel == 'TIE':
                print("    🔄 TIE (exclu)")
                mains_tie += 1
                continue
                
            # Tenter l'analyse
            try:
                partie_temp = {
                    'numero': 0,
                    'mains': mains_analyse
                }
                analyse = analyseur.analyser_partie_fractale(partie_temp)
                prediction_info = analyse.prediction_main_suivante
                
                print(f"    🎯 Prédiction: {prediction_info}")
                
                if prediction_info in ['INDETERMINE', 'ALEATOIRE', 'ATTENDRE']:
                    print("    ❌ Prédiction indéterminée")
                    predictions_indeterminees += 1
                else:
                    print("    ✅ Prédiction valide")
                    predictions_valides += 1
                    
            except Exception as e:
                print(f"    ❌ Erreur analyse: {e}")
                erreurs_analyse += 1
    
    # Résumé du diagnostic
    print(f"\n📊 RÉSUMÉ DU DIAGNOSTIC")
    print("=" * 30)
    print(f"Parties analysées: {parties_analysees}")
    print(f"Parties trop courtes: {parties_trop_courtes}")
    print(f"Mains analysées: {mains_analysees}")
    print(f"Mains sans résultat: {mains_sans_resultat}")
    print(f"Mains TIE: {mains_tie}")
    print(f"Prédictions indéterminées: {predictions_indeterminees}")
    print(f"Prédictions valides: {predictions_valides}")
    print(f"Erreurs d'analyse: {erreurs_analyse}")
    
    # Diagnostic des problèmes
    print(f"\n🔍 DIAGNOSTIC DES PROBLÈMES")
    print("=" * 30)
    
    if parties_trop_courtes > 0:
        print(f"⚠️  {parties_trop_courtes} parties trop courtes (< 10 mains)")
        
    if mains_sans_resultat > 0:
        print(f"⚠️  {mains_sans_resultat} mains sans résultat index3_result")
        
    if predictions_indeterminees > predictions_valides:
        print(f"⚠️  Trop de prédictions indéterminées ({predictions_indeterminees} vs {predictions_valides} valides)")
        
    if erreurs_analyse > 0:
        print(f"❌ {erreurs_analyse} erreurs d'analyse")
        
    # Test de la structure des données
    print(f"\n🔬 TEST STRUCTURE DES DONNÉES")
    print("=" * 30)
    
    if len(parties) > 0:
        premiere_partie = parties[0]
        print(f"Clés partie: {list(premiere_partie.keys())}")
        
        mains = premiere_partie.get('mains', [])
        if len(mains) > 0:
            premiere_main = mains[0]
            print(f"Clés main: {list(premiere_main.keys())}")
            print(f"Exemple index3_result: {premiere_main.get('index3_result')}")

def test_analyse_simple():
    """
    🧪 TEST D'ANALYSE SIMPLE SUR UNE PARTIE
    """
    print(f"\n🧪 TEST D'ANALYSE SIMPLE")
    print("=" * 30)
    
    analyseur = AnalyseurFractalBaccarat()
    dataset = analyseur.charger_dataset()
    parties = dataset.get('parties', [])
    
    if len(parties) > 0:
        partie_test = parties[0]
        print(f"Test sur partie 1 avec {len(partie_test.get('mains', []))} mains")
        
        try:
            resultat = analyseur.analyser_partie_fractale(partie_test)
            print(f"✅ Analyse réussie")
            print(f"Prédiction: {resultat.prediction_main_suivante}")
            print(f"Confiance: {resultat.confiance_prediction}")
            print(f"Probabilité: {resultat.probabilite_prediction}")
        except Exception as e:
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    try:
        diagnostic_complet()
        test_analyse_simple()
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
