#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 PRÉDICTEUR MAIN PAR MAIN POUR BACCARAT
=========================================
Utilise analyseur_fractal_baccarat.py pour prédire chaque main dans un fichier JSON
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any
from analyseur_fractal_baccarat import AnalyseurFractalBaccarat

class PredicteurMainParMain:
    """
    🎯 PRÉDICTEUR MAIN PAR MAIN
    Génère des prédictions pour chaque main d'un dataset
    """
    
    def __init__(self):
        self.analyseur = AnalyseurFractalBaccarat()
        self.predictions = []
        self.statistiques = {
            'total_mains': 0,
            'predictions_generees': 0,
            'predictions_banker': 0,
            'predictions_player': 0,
            'predictions_tie': 0,
            'predictions_indeterminees': 0,
            'confiance_moyenne': 0.0
        }
    
    def predire_fichier_json(self, chemin_fichier: str, min_mains_historique: int = 10) -> Dict:
        """
        🎯 PRÉDIT TOUTES LES MAINS D'UN FICHIER JSON
        
        Args:
            chemin_fichier: Chemin vers le fichier JSON
            min_mains_historique: Nombre minimum de mains historiques pour prédiction
            
        Returns:
            Dict avec toutes les prédictions et statistiques
        """
        print(f"🎯 PRÉDICTEUR MAIN PAR MAIN - DÉMARRAGE")
        print("=" * 50)
        
        # Charger le dataset
        print(f"📂 Chargement: {chemin_fichier}")
        with open(chemin_fichier, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        parties = dataset.get('parties', [])
        print(f"✅ Dataset chargé: {len(parties)} parties")
        
        # Traiter chaque partie
        for idx_partie, partie in enumerate(parties):
            print(f"\n📊 Partie {idx_partie + 1}/{len(parties)}")
            self._predire_partie(partie, idx_partie, min_mains_historique)
        
        # Calculer statistiques finales
        self._calculer_statistiques_finales()
        
        # Préparer résultats
        resultats = {
            'metadata': {
                'fichier_source': chemin_fichier,
                'timestamp': datetime.now().isoformat(),
                'min_mains_historique': min_mains_historique,
                'total_parties': len(parties)
            },
            'statistiques': self.statistiques,
            'predictions': self.predictions
        }
        
        return resultats
    
    def _predire_partie(self, partie: Dict, idx_partie: int, min_mains_historique: int):
        """
        🎯 PRÉDIT TOUTES LES MAINS D'UNE PARTIE
        """
        mains = partie.get('mains', [])
        numero_partie = partie.get('numero', idx_partie)
        
        print(f"  Mains: {len(mains)}")
        
        if len(mains) < min_mains_historique + 1:
            print(f"  ⚠️ Trop courte (< {min_mains_historique + 1} mains)")
            return
        
        # Prédire chaque main (à partir de min_mains_historique)
        for i in range(min_mains_historique, len(mains)):
            mains_historique = mains[:i]
            main_cible = mains[i]
            
            # Générer prédiction
            prediction = self._generer_prediction(
                mains_historique, 
                numero_partie, 
                i,
                main_cible
            )
            
            if prediction:
                self.predictions.append(prediction)
                self.statistiques['predictions_generees'] += 1
                
                # Compter par type
                pred_type = prediction['prediction']['prediction']
                if pred_type == 'BANKER':
                    self.statistiques['predictions_banker'] += 1
                elif pred_type == 'PLAYER':
                    self.statistiques['predictions_player'] += 1
                elif pred_type == 'TIE':
                    self.statistiques['predictions_tie'] += 1
                else:
                    self.statistiques['predictions_indeterminees'] += 1
            
            self.statistiques['total_mains'] += 1
        
        print(f"  ✅ {len(mains) - min_mains_historique} prédictions générées")
    
    def _generer_prediction(self, mains_historique: List[Dict], numero_partie: int, 
                          index_main: int, main_cible: Dict) -> Dict:
        """
        🎯 GÉNÈRE UNE PRÉDICTION POUR UNE MAIN
        """
        try:
            # Créer partie temporaire pour analyse
            partie_temp = {
                'numero': numero_partie,
                'mains': mains_historique
            }
            
            # Analyser avec le système fractal
            analyse = self.analyseur.analyser_partie_fractale(partie_temp)
            
            # Extraire prédiction
            prediction_info = {
                'prediction': analyse.prediction_main_suivante,
                'confiance': analyse.confiance_prediction,
                'probabilite': getattr(analyse, 'probabilite_prediction', 0.0),
                'hurst_resultats': analyse.hurst_resultats,
                'hurst_index5': analyse.hurst_index5,
                'type_persistance': analyse.type_persistance,
                'qualite_estimation': analyse.qualite_estimation
            }
            
            # Résultat réel (si disponible)
            resultat_reel = main_cible.get('index3_result', 'UNKNOWN')
            
            # Métadonnées
            metadata = {
                'numero_partie': numero_partie,
                'index_main': index_main,
                'mains_historique': len(mains_historique),
                'timestamp_prediction': datetime.now().isoformat(),
                'resultat_reel': resultat_reel
            }
            
            return {
                'metadata': metadata,
                'prediction': prediction_info,
                'evaluation': self._evaluer_prediction_simple(prediction_info, resultat_reel)
            }
            
        except Exception as e:
            print(f"    ❌ Erreur prédiction main {index_main}: {e}")
            return None
    
    def _evaluer_prediction_simple(self, prediction: Dict, resultat_reel: str) -> Dict:
        """
        📊 ÉVALUATION SIMPLE D'UNE PRÉDICTION
        """
        pred_type = prediction['prediction']
        
        if resultat_reel == 'UNKNOWN' or pred_type in ['INDETERMINE', 'ALEATOIRE', 'ATTENDRE']:
            return {
                'evaluable': False,
                'correct': None,
                'raison': 'Non évaluable'
            }
        
        if resultat_reel == 'TIE':
            return {
                'evaluable': False,
                'correct': None,
                'raison': 'TIE (remboursement)'
            }
        
        correct = (pred_type == resultat_reel)
        
        return {
            'evaluable': True,
            'correct': correct,
            'raison': 'Évalué'
        }
    
    def _calculer_statistiques_finales(self):
        """
        📊 CALCULE LES STATISTIQUES FINALES
        """
        if self.statistiques['predictions_generees'] > 0:
            # Confiance moyenne
            confiances = [p['prediction']['confiance'] for p in self.predictions 
                         if p and 'prediction' in p]
            if confiances:
                self.statistiques['confiance_moyenne'] = sum(confiances) / len(confiances)
            
            # Taux de réussite (si évaluable)
            evaluables = [p for p in self.predictions 
                         if p and p['evaluation']['evaluable']]
            
            if evaluables:
                corrects = [p for p in evaluables if p['evaluation']['correct']]
                self.statistiques['predictions_evaluables'] = len(evaluables)
                self.statistiques['predictions_correctes'] = len(corrects)
                self.statistiques['taux_reussite'] = (len(corrects) / len(evaluables)) * 100
            else:
                self.statistiques['predictions_evaluables'] = 0
                self.statistiques['predictions_correctes'] = 0
                self.statistiques['taux_reussite'] = 0.0
    
    def sauvegarder_predictions(self, resultats: Dict, nom_fichier: str = None):
        """
        💾 SAUVEGARDE LES PRÉDICTIONS
        """
        if not nom_fichier:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nom_fichier = f"predictions_main_par_main_{timestamp}.json"
        
        with open(nom_fichier, 'w', encoding='utf-8') as f:
            json.dump(resultats, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"💾 Prédictions sauvegardées: {nom_fichier}")
        return nom_fichier

def main():
    """
    🎯 FONCTION PRINCIPALE
    """
    print("🎯 PRÉDICTEUR MAIN PAR MAIN BACCARAT")
    print("=" * 40)
    
    # Fichier par défaut
    fichier_dataset = "dataset_baccarat_lupasco_20250626_044753.json"
    
    if not os.path.exists(fichier_dataset):
        print(f"❌ Fichier non trouvé: {fichier_dataset}")
        return
    
    # Créer prédicteur
    predicteur = PredicteurMainParMain()
    
    # Options
    print(f"📂 Fichier: {fichier_dataset}")
    print(f"🔧 Options:")
    print(f"  1. Prédictions complètes (toutes les mains)")
    print(f"  2. Échantillon (100 premières parties)")
    print(f"  3. Test rapide (10 parties)")
    
    choix = input("Votre choix (1/2/3): ").strip()
    
    # Charger et filtrer si nécessaire
    with open(fichier_dataset, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    if choix == '2':
        dataset['parties'] = dataset['parties'][:100]
        print("📊 Mode échantillon: 100 parties")
    elif choix == '3':
        dataset['parties'] = dataset['parties'][:10]
        print("📊 Mode test: 10 parties")
    else:
        print("📊 Mode complet: toutes les parties")
    
    # Sauvegarder dataset temporaire si filtré
    if choix in ['2', '3']:
        fichier_temp = f"temp_dataset_{choix}.json"
        with open(fichier_temp, 'w', encoding='utf-8') as f:
            json.dump(dataset, f)
        fichier_dataset = fichier_temp
    
    # Générer prédictions
    resultats = predicteur.predire_fichier_json(fichier_dataset)
    
    # Afficher statistiques
    print(f"\n📊 STATISTIQUES FINALES")
    print("=" * 30)
    stats = resultats['statistiques']
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        else:
            print(f"{key}: {value}")
    
    # Sauvegarder
    fichier_resultats = predicteur.sauvegarder_predictions(resultats)
    
    # Nettoyer fichier temporaire
    if choix in ['2', '3'] and os.path.exists(fichier_temp):
        os.remove(fichier_temp)
    
    print(f"\n✅ Prédictions terminées!")
    print(f"📄 Résultats: {fichier_resultats}")

if __name__ == "__main__":
    main()
