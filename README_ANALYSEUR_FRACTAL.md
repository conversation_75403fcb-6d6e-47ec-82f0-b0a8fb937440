# 🔬 ANALYSEUR FRACTAL BACCARAT LUPASCO

## Vue d'ensemble

L'Analyseur Fractal Baccarat Lupasco est un système d'analyse avancé qui applique les techniques fractales les plus sophistiquées aux données de baccarat. Il transforme le programme `Anb.py` en un outil d'analyse fractale complet, équivalent aux meilleurs logiciels d'analyse fractale disponibles.

## 🎯 Fonctionnalités Principales

### 1. Analyse Fractale Complète
- **Dimension Fractale** : Calcul par méthode box-counting
- **Exposant de Hurst** : Analyse R/S pour détecter la persistance
- **DFA (Detrended Fluctuation Analysis)** : Analyse des corrélations long-terme
- **Analyse Multifractale** : Spectre multifractal et asymétrie
- **Entropie** : Shannon et Rényi pour mesurer l'information
- **Complexité Lempel-Ziv** : Mesure de la complexité algorithmique

### 2. Prédictions Fractales
- **Méthode Hurst** : Prédiction basée sur l'exposant de Hurst
- **Méthode DFA** : Prédiction basée sur l'analyse DFA
- **Méthode Patterns** : Détection de patterns fractals
- **Méthode Multifractale** : Analyse du spectre multifractal
- **Méthode Combinée** : Fusion de toutes les approches

### 3. Rapports et Exports
- **Rapport Fractal Complet** : Analyse détaillée en format TXT
- **Séquence Complète** : Toutes les mains INDEX5 de la partie
- **Autocorrélations** : Analyse des dépendances temporelles
- **Données DFA** : Échelles et fluctuations détaillées

## 🚀 Utilisation

### Utilisation Basique

```python
from Anb import AnalyseurBaccaratLupasco

# Initialiser l'analyseur
analyseur = AnalyseurBaccaratLupasco("dataset_baccarat_lupasco_20250629_144545.json")

# Charger les données
if analyseur.charger_donnees():
    # Obtenir les parties disponibles
    parties = analyseur.obtenir_parties_disponibles()
    
    # Analyser une partie
    analyse = analyseur.analyser_fractales_partie(parties[0])
    
    # Générer le rapport complet
    rapport = analyseur.generer_rapport_fractal_complet(parties[0])
```

### Analyse Complète

```python
# Exécuter le programme principal
python Anb.py

# Ou utiliser le script de test
python test_analyseur_fractal.py
```

## 📊 Interprétation des Résultats

### Exposant de Hurst
- **H > 0.6** : Série persistante (tendances long-terme)
- **H < 0.4** : Série anti-persistante (retours à la moyenne)
- **H ≈ 0.5** : Série aléatoire (mouvement brownien)

### DFA Alpha
- **α > 1.0** : Corrélations long-terme fortes
- **α < 0.5** : Anti-corrélations présentes
- **α ≈ 0.5** : Comportement proche du bruit blanc

### Entropie Shannon
- **Haute entropie** : Série imprévisible
- **Basse entropie** : Patterns détectables

### Dimension Fractale
- **D proche de 1** : Structure simple
- **D proche de 2** : Structure complexe, fractale

## 🎯 Prédictions

Le système propose 5 méthodes de prédiction :

1. **HURST** : Basée sur l'exposant de Hurst
2. **DFA** : Basée sur l'analyse DFA
3. **PATTERNS** : Détection de patterns récurrents
4. **MULTIFRACTAL** : Analyse du spectre multifractal
5. **COMBINE** : Fusion intelligente de toutes les méthodes

Chaque prédiction inclut un niveau de confiance (0-100%).

## 📄 Fichiers Générés

### Rapport Fractal Principal
- **Nom** : `rapport_fractal_partie_X_YYYYMMDD_HHMMSS.txt`
- **Contenu** : Analyse complète avec toutes les métriques fractales
- **Sections** :
  - Caractéristiques fractales
  - Entropie et complexité
  - Analyse multifractale
  - Patterns détectés
  - Prédictions
  - Données détaillées
  - Autocorrélations
  - Analyse DFA

### Export TXT Traditionnel
- **Nom** : `index5_export.txt`
- **Contenu** : Fichier minimal pour analyses spécialisées

## 🔬 Méthodes Avancées

### Analyse de Toutes les Parties
```python
toutes_analyses = analyseur.analyser_toutes_parties_fractales()
```

### Comparaison entre Parties
```python
comparaison = analyseur.comparer_parties_fractales([1, 2, 3])
```

### Prédiction Spécifique
```python
prediction = analyseur.predire_prochaine_main_fractal(1, "multifractal")
```

## 📈 Exemples de Résultats

### Analyse Typique
```
Dimension fractale : 0.6889
Exposant de Hurst : 0.6303 → Série PERSISTANTE
DFA Alpha : 1.0900 → Corrélations long-terme FORTES
Entropie Shannon : 3.5458 → Série très IMPRÉVISIBLE
Complexité L-Z : 0.9878
```

### Prédiction Typique
```
MULTIFRACTAL: 0_A_BANKER (confiance: 70.0%)
COMBINE     : 0_A_BANKER (confiance: 100.0%)
```

## 🛠️ Configuration Requise

- Python 3.7+
- NumPy
- Pandas
- SciPy
- Fichier JSON de données baccarat

## 📚 Références Scientifiques

L'analyseur implémente les techniques fractales de pointe basées sur :
- Analyse DFA (Peng et al., 1994)
- Analyse multifractale (Kantelhardt et al., 2002)
- Exposant de Hurst (Mandelbrot, 1982)
- Complexité Lempel-Ziv (Lempel & Ziv, 1976)

## 🎉 Conclusion

Cet analyseur fractal transforme `Anb.py` en un outil d'analyse fractale professionnel, capable de révéler les structures cachées dans les données de baccarat et de développer des outils de prédiction basés sur les fractales.
