#!/usr/bin/env python3
"""
PRÉDICTEUR FRACTAL TEMPS RÉEL POUR BACCARAT
Utilise l'analyse fractale pour prédictions en temps réel
Basé sur l'exposant de Hurst et les patterns de persistance
"""

import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from analyseur_fractal_baccarat import AnalyseurFractalBaccarat, ResultatAnalyseFractale

@dataclass
class PredictionTempsReel:
    """Structure pour une prédiction en temps réel"""
    
    # Identification
    numero_partie: int
    numero_main_actuelle: int
    
    # Analyse fractale
    hurst_exponent: float
    type_persistance: str
    dimension_fractale: float
    
    # Prédiction
    prediction_main_suivante: str
    probabilite: float
    confiance: float
    
    # Contexte
    nb_mains_analysees: int
    derniers_resultats: List[str]
    tendance_actuelle: str
    
    # Recommandations
    recommandation_mise: str
    niveau_risque: str
    
    # Métadonnées
    timestamp: str

class PredicteurFractalTempsReel:
    """
    Prédicteur fractal pour utilisation en temps réel
    Analyse les parties en cours et génère des prédictions
    """
    
    def __init__(self):
        """Initialise le prédicteur"""
        self.analyseur_fractal = AnalyseurFractalBaccarat()
        self.historique_predictions = []
        
        # Seuils de décision
        self.seuil_confiance_haute = 0.4
        self.seuil_hurst_persistant = 0.65
        self.seuil_hurst_anti_persistant = 0.4
        
        print("🔮 Prédicteur Fractal Temps Réel initialisé")
    
    def analyser_partie_en_cours(self, mains_jouees: List[Dict]) -> PredictionTempsReel:
        """
        Analyse une partie en cours et génère une prédiction
        
        Args:
            mains_jouees: Liste des mains déjà jouées
            
        Returns:
            PredictionTempsReel: Prédiction pour la main suivante
        """
        if len(mains_jouees) < 4:
            return self._prediction_insuffisante(len(mains_jouees))
        
        # Créer une structure de partie temporaire
        partie_temp = {
            'partie_number': 9999,  # Numéro temporaire
            'mains': [{'main_number': None, 'index3_result': '', 'index5_combined': ''}] + mains_jouees
        }
        
        # Analyser avec l'analyseur fractal
        resultat_fractal = self.analyseur_fractal.analyser_partie_fractale(partie_temp)
        
        # Extraire les derniers résultats
        derniers_resultats = [main.get('index3_result', '') for main in mains_jouees[-5:]]
        
        # Déterminer la tendance actuelle
        tendance = self._determiner_tendance(derniers_resultats)
        
        # Générer la prédiction temps réel
        prediction = PredictionTempsReel(
            numero_partie=9999,
            numero_main_actuelle=len(mains_jouees),
            hurst_exponent=resultat_fractal.hurst_resultats,
            type_persistance=resultat_fractal.type_persistance_resultats,
            dimension_fractale=resultat_fractal.dimension_fractale_resultats,
            prediction_main_suivante=resultat_fractal.prediction_main_suivante,
            probabilite=resultat_fractal.probabilite_prediction,
            confiance=resultat_fractal.confiance_prediction,
            nb_mains_analysees=resultat_fractal.nb_mains_valides,
            derniers_resultats=derniers_resultats,
            tendance_actuelle=tendance,
            recommandation_mise=self._generer_recommandation_mise(resultat_fractal),
            niveau_risque=self._evaluer_niveau_risque(resultat_fractal),
            timestamp=datetime.now().isoformat()
        )
        
        # Ajouter à l'historique
        self.historique_predictions.append(prediction)
        
        return prediction
    
    def _prediction_insuffisante(self, nb_mains: int) -> PredictionTempsReel:
        """Génère une prédiction par défaut quand il n'y a pas assez de données"""
        return PredictionTempsReel(
            numero_partie=9999,
            numero_main_actuelle=nb_mains,
            hurst_exponent=np.nan,
            type_persistance='indetermine',
            dimension_fractale=np.nan,
            prediction_main_suivante='ATTENDRE',
            probabilite=0.0,
            confiance=0.0,
            nb_mains_analysees=nb_mains,
            derniers_resultats=[],
            tendance_actuelle='indeterminee',
            recommandation_mise='ATTENDRE',
            niveau_risque='ELEVE',
            timestamp=datetime.now().isoformat()
        )
    
    def _determiner_tendance(self, derniers_resultats: List[str]) -> str:
        """Détermine la tendance actuelle basée sur les derniers résultats"""
        if len(derniers_resultats) < 3:
            return 'indeterminee'
        
        # Compter les occurrences récentes
        banker_count = derniers_resultats.count('BANKER')
        player_count = derniers_resultats.count('PLAYER')
        tie_count = derniers_resultats.count('TIE')
        
        if banker_count > player_count + 1:
            return 'BANKER_dominant'
        elif player_count > banker_count + 1:
            return 'PLAYER_dominant'
        elif tie_count >= 2:
            return 'TIE_frequent'
        else:
            return 'equilibree'
    
    def _generer_recommandation_mise(self, resultat: ResultatAnalyseFractale) -> str:
        """Génère une recommandation de mise basée sur l'analyse fractale"""
        if np.isnan(resultat.hurst_resultats):
            return 'ATTENDRE'
        
        confiance = resultat.confiance_prediction
        hurst = resultat.hurst_resultats
        prediction = resultat.prediction_main_suivante
        
        if prediction == 'ALEATOIRE' or prediction == 'INDETERMINE':
            return 'MISE_MINIMALE'
        
        if confiance > self.seuil_confiance_haute and hurst > self.seuil_hurst_persistant:
            return f'MISE_FORTE_{prediction}'
        elif confiance > 0.25 and hurst > 0.55:
            return f'MISE_MODEREE_{prediction}'
        elif confiance > 0.15:
            return f'MISE_FAIBLE_{prediction}'
        else:
            return 'MISE_MINIMALE'
    
    def _evaluer_niveau_risque(self, resultat: ResultatAnalyseFractale) -> str:
        """Évalue le niveau de risque de la prédiction"""
        if np.isnan(resultat.hurst_resultats):
            return 'ELEVE'
        
        confiance = resultat.confiance_prediction
        qualite = resultat.qualite_estimation
        
        if confiance > 0.4 and qualite in ['excellent', 'bon']:
            return 'FAIBLE'
        elif confiance > 0.25 and qualite in ['excellent', 'bon', 'moyen']:
            return 'MODERE'
        elif confiance > 0.15:
            return 'ELEVE'
        else:
            return 'TRES_ELEVE'
    
    def afficher_prediction_detaillee(self, prediction: PredictionTempsReel):
        """Affiche une prédiction de manière détaillée"""
        print(f"\n🔮 PRÉDICTION FRACTALE - MAIN {prediction.numero_main_actuelle + 1}")
        print("=" * 60)
        
        # Analyse fractale
        print(f"📊 ANALYSE FRACTALE:")
        if not np.isnan(prediction.hurst_exponent):
            print(f"   Exposant de Hurst: {prediction.hurst_exponent:.4f}")
            print(f"   Type de persistance: {prediction.type_persistance}")
            print(f"   Dimension fractale: {prediction.dimension_fractale:.4f}")
        else:
            print(f"   Données insuffisantes pour analyse fractale")
        
        # Contexte
        print(f"\n📈 CONTEXTE:")
        print(f"   Mains analysées: {prediction.nb_mains_analysees}")
        print(f"   Tendance actuelle: {prediction.tendance_actuelle}")
        if prediction.derniers_resultats:
            print(f"   Derniers résultats: {' → '.join(prediction.derniers_resultats)}")
        
        # Prédiction
        print(f"\n🎯 PRÉDICTION:")
        print(f"   Main suivante: {prediction.prediction_main_suivante}")
        print(f"   Probabilité: {prediction.probabilite:.3f}")
        print(f"   Confiance: {prediction.confiance:.3f}")
        
        # Recommandations
        print(f"\n💡 RECOMMANDATIONS:")
        print(f"   Mise recommandée: {prediction.recommandation_mise}")
        print(f"   Niveau de risque: {prediction.niveau_risque}")
        
        # Interprétation
        self._afficher_interpretation(prediction)
    
    def _afficher_interpretation(self, prediction: PredictionTempsReel):
        """Affiche l'interprétation de la prédiction"""
        print(f"\n🧠 INTERPRÉTATION:")
        
        if np.isnan(prediction.hurst_exponent):
            print("   ⚠️  Pas assez de données pour une analyse fiable")
            print("   📝 Recommandation: Attendre plus de mains avant de miser")
            return
        
        hurst = prediction.hurst_exponent
        confiance = prediction.confiance
        
        if hurst > 0.65:
            print("   🔥 Processus très persistant détecté")
            print("   📝 Les tendances ont tendance à continuer")
            if confiance > 0.4:
                print("   ✅ Confiance élevée - Opportunité de mise forte")
            else:
                print("   ⚠️  Confiance modérée - Prudence recommandée")
        
        elif hurst < 0.45:
            print("   🔄 Processus anti-persistant détecté")
            print("   📝 Retour à la moyenne probable")
            print("   💡 Stratégie contrarian recommandée")
        
        else:
            print("   🎲 Processus proche du hasard")
            print("   📝 Prédictibilité limitée")
            print("   ⚠️  Mises conservatrices recommandées")
        
        # Niveau de risque
        if prediction.niveau_risque == 'FAIBLE':
            print("   🟢 Risque faible - Prédiction fiable")
        elif prediction.niveau_risque == 'MODERE':
            print("   🟡 Risque modéré - Prédiction acceptable")
        elif prediction.niveau_risque == 'ELEVE':
            print("   🟠 Risque élevé - Prudence requise")
        else:
            print("   🔴 Risque très élevé - Éviter les grosses mises")

def simuler_partie_temps_reel():
    """Simule une utilisation en temps réel avec une partie du dataset"""
    print("🎮 SIMULATION PRÉDICTION TEMPS RÉEL")
    print("=" * 50)
    
    # Charger une partie du dataset pour simulation
    predicteur = PredicteurFractalTempsReel()
    dataset = predicteur.analyseur_fractal.charger_dataset()
    
    # Prendre une partie aléatoire
    import random
    partie_test = random.choice(dataset['parties'])
    mains_partie = [main for main in partie_test['mains'] if main.get('main_number') is not None]
    
    print(f"📊 Simulation avec partie {partie_test['partie_number']}")
    print(f"📈 {len(mains_partie)} mains disponibles")
    
    # Simuler l'analyse progressive
    for i in range(4, min(len(mains_partie), 15), 3):  # Analyser toutes les 3 mains
        mains_jouees = mains_partie[:i]
        
        print(f"\n" + "="*60)
        print(f"🕐 ANALYSE APRÈS {i} MAINS JOUÉES")
        
        prediction = predicteur.analyser_partie_en_cours(mains_jouees)
        predicteur.afficher_prediction_detaillee(prediction)
        
        # Afficher le résultat réel si disponible
        if i < len(mains_partie):
            main_suivante = mains_partie[i]
            resultat_reel = main_suivante.get('index3_result', 'INCONNU')
            print(f"\n✅ RÉSULTAT RÉEL: {resultat_reel}")
            
            if prediction.prediction_main_suivante == resultat_reel:
                print("🎯 PRÉDICTION CORRECTE !")
            elif prediction.prediction_main_suivante in ['ALEATOIRE', 'INDETERMINE']:
                print("⚪ Prédiction neutre")
            else:
                print("❌ Prédiction incorrecte")
        
        # Pause pour simulation
        input("\n⏸️  Appuyez sur Entrée pour continuer...")

def main():
    """Fonction principale"""
    print("🔮 PRÉDICTEUR FRACTAL TEMPS RÉEL - BACCARAT")
    print("=" * 60)
    
    print("\n🎯 OPTIONS DISPONIBLES:")
    print("1. Simulation temps réel avec partie du dataset")
    print("2. Analyse manuelle (saisie des mains)")
    print("3. Quitter")
    
    choix = input("\nVotre choix (1/2/3): ").strip()
    
    if choix == "1":
        simuler_partie_temps_reel()
    
    elif choix == "2":
        print("\n📝 Mode analyse manuelle non implémenté dans cette version")
        print("   Utilisez la simulation pour voir le fonctionnement")
    
    else:
        print("👋 Au revoir !")

if __name__ == "__main__":
    main()
