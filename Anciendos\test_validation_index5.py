#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 SCRIPT DE TEST RAPIDE POUR VALIDATION INDEX5
Test des améliorations selon le plan de validation défini

Exécute les tests comparatifs entre méthode classique et INDEX5 complète
selon les métriques d'amélioration attendues du plan.
"""

import sys
import os
from analyseur_fractal_baccarat import AnalyseurFractalBaccarat

def test_validation_rapide():
    """
    🚀 TEST DE VALIDATION RAPIDE
    Exécute la validation complète et affiche les résultats
    """
    print("🧪 TEST DE VALIDATION INDEX5 - DÉMARRAGE")
    print("=" * 60)
    
    # Initialiser l'analyseur
    analyseur = AnalyseurFractalBaccarat()
    
    # Charger les données (utiliser le fichier disponible)
    nom_fichier = "dataset_baccarat_lupasco_20250626_044753.json"
    
    if not os.path.exists(nom_fichier):
        print(f"❌ Fichier {nom_fichier} non trouvé")
        print("   Assurez-vous que le fichier de données est présent")
        return False
    
    print(f"📂 Chargement des données: {nom_fichier}")
    # L'analyseur utilise son dataset par défaut
    dataset = analyseur.charger_dataset()
    
    if not dataset:
        print("❌ Erreur lors du chargement des données")
        return False
    
    print(f"✅ Données chargées: {len(dataset.get('parties', []))} parties")
    
    # Exécuter la validation complète
    print(f"\n🧪 EXÉCUTION DE LA VALIDATION COMPLÈTE")
    print("   Cela peut prendre quelques minutes...")
    
    try:
        resultats_validation = analyseur.executer_validation_complete(dataset)
        
        # Sauvegarder les résultats
        nom_validation = "validation_index5_resultats.json"
        import json
        with open(nom_validation, 'w', encoding='utf-8') as f:
            json.dump(resultats_validation, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 Résultats sauvegardés: {nom_validation}")
        
        # Afficher le résumé des résultats
        afficher_resume_validation(resultats_validation)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la validation: {e}")
        import traceback
        traceback.print_exc()
        return False

def afficher_resume_validation(resultats: dict):
    """
    📊 AFFICHE UN RÉSUMÉ DES RÉSULTATS DE VALIDATION
    """
    print(f"\n🎯 RÉSUMÉ DES RÉSULTATS DE VALIDATION")
    print("=" * 50)
    
    # Métriques principales
    taux_classique = resultats.get('taux_reussite_classique', 0)
    taux_index5 = resultats.get('taux_reussite_index5', 0)
    amelioration_taux = resultats.get('amelioration_taux', 0)
    
    confiance_classique = resultats.get('confiance_moyenne_classique', 0)
    confiance_index5 = resultats.get('confiance_moyenne_index5', 0)
    amelioration_confiance = resultats.get('amelioration_confiance', 0)
    
    exploitables_classique = resultats.get('parties_exploitables_classique', 0)
    exploitables_index5 = resultats.get('parties_exploitables_index5', 0)
    amelioration_exploitables = resultats.get('amelioration_exploitables', 0)
    
    print(f"📈 TAUX DE RÉUSSITE:")
    print(f"   Classique: {taux_classique:.1f}%")
    print(f"   INDEX5: {taux_index5:.1f}%")
    print(f"   Amélioration: {amelioration_taux:+.1f}%")
    
    print(f"\n🎯 CONFIANCE MOYENNE:")
    print(f"   Classique: {confiance_classique:.3f}")
    print(f"   INDEX5: {confiance_index5:.3f}")
    print(f"   Amélioration: {amelioration_confiance:+.3f}")
    
    print(f"\n🔍 PARTIES EXPLOITABLES:")
    print(f"   Classique: {exploitables_classique:.1f}%")
    print(f"   INDEX5: {exploitables_index5:.1f}%")
    print(f"   Amélioration: {amelioration_exploitables:+.1f}%")
    
    # Évaluation du succès
    succes_taux = amelioration_taux >= 2.0
    succes_confiance = amelioration_confiance >= 0.15
    succes_exploitables = amelioration_exploitables >= 15.0
    
    print(f"\n✅ ÉVALUATION DU SUCCÈS:")
    print(f"   Taux (≥+2%): {'✅ OUI' if succes_taux else '❌ NON'}")
    print(f"   Confiance (≥+0.15): {'✅ OUI' if succes_confiance else '❌ NON'}")
    print(f"   Exploitables (≥+15%): {'✅ OUI' if succes_exploitables else '❌ NON'}")
    
    succes_global = succes_taux and succes_confiance and succes_exploitables
    print(f"\n🎯 RÉSULTAT GLOBAL: {'✅ SUCCÈS' if succes_global else '❌ ÉCHEC'}")
    
    # Métriques INDEX5 spécifiques
    avantage_granularite = resultats.get('avantage_granularite_moyen', 0)
    predictions_index5 = resultats.get('predictions_index5_utilisees', 0)
    
    print(f"\n🚀 MÉTRIQUES INDEX5:")
    print(f"   Avantage granularité: {avantage_granularite:.1f}%")
    print(f"   Prédictions INDEX5: {predictions_index5}")

def main():
    """
    🎯 FONCTION PRINCIPALE
    """
    print("🧪 SCRIPT DE TEST VALIDATION INDEX5")
    print("Teste les améliorations selon le plan de validation")
    print()
    
    # Exécuter le test
    succes = test_validation_rapide()
    
    if succes:
        print(f"\n✅ Test de validation terminé avec succès")
        print("📊 Consultez les fichiers de résultats pour plus de détails")
    else:
        print(f"\n❌ Test de validation échoué")
        print("🔍 Vérifiez les erreurs ci-dessus")
    
    return succes

if __name__ == "__main__":
    main()
