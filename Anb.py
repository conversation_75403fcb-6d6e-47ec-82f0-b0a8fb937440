#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    ANALYSEUR BACCARAT LUPASCO - BASE MODULAIRE               ║
║                           ARCHITECTURE OPTIMISÉE v2.0                       ║
╚══════════════════════════════════════════════════════════════════════════════╝

DESCRIPTION :
    Programme d'analyse modulaire et extensible pour les données de baccarat Lupasco.
    Optimisé pour multiprocessing (8 cœurs) et gros datasets (28GB RAM).
    Architecture structurée pour navigation optimale.

FONCTIONNALITÉS PRINCIPALES :
    ✅ Chargement optimisé avec orjson/ijson (technique VDIFF.py)
    ✅ Multiprocessing avec 8 cœurs CPU
    ✅ Délimitation correcte des parties (excluant main dummy)
    ✅ Analyse fractale complète avec Numba JIT
    ✅ Extraction INDEX5 optimisée
    ✅ Architecture modulaire extensible
    ✅ Gestion d'erreurs robuste

ARCHITECTURE DU FICHIER - NAVIGATION OPTIMISÉE :
    📦 SECTION 1 : IMPORTS ET CONFIGURATION (lignes ~45-105)
        1.1 Imports standards et scientifiques
        1.2 Imports conditionnels performance (or<PERSON><PERSON>, numba, ijson)
        1.3 Configuration multiprocessing (8 cœurs, 28GB RAM)

    🔧 SECTION 2 : OPTIMISATIONS PERFORMANCE (lignes ~107-168)
        2.1 Fonctions Numba JIT ultra-rapides
        2.2 Versions fallback sans Numba

    🏗️ SECTION 3 : UTILITAIRES CHARGEMENT DATASET (lignes ~170-312)
        3.1 Fonction multiprocessing pour traitement chunks
        3.2 Détection automatique du dataset

    📊 SECTION 4 : STRUCTURES DE DONNÉES (lignes ~313-406)
        4.1 Structures pour analyse fractale
        4.2 Structures pour données baccarat
        4.3 Structures pour statistiques

    🎯 SECTION 5 : CLASSE PRINCIPALE D'ANALYSE (lignes ~407-890)
        5.1 Classe analyseur principal
        5.2 Méthodes de chargement et traitement
        5.3 Méthodes d'extraction et filtrage

    🔍 SECTION 6 : MÉTHODES D'ANALYSE FRACTALE (lignes ~891-1765)
        6.1 Analyse fractale principale

    📈 SECTION 7 : MÉTHODES DE REPORTING (lignes ~1766-2363)
        7.1 Génération de rapports

    🧪 SECTION 8 : EXEMPLES ET ANALYSES SPÉCIALISÉES (lignes ~2364-2421)
        8.1 Analyses de séquences

    🚀 SECTION 9 : FONCTION PRINCIPALE ET TESTS (lignes ~2422-fin)
        9.1 Fonction principale
        9.2 Point d'entrée du programme

USAGE :
    analyseur = AnalyseurBaccaratLupasco()  # Détection automatique du dataset
    analyseur.charger_donnees()
    analyseur.executer_analyse_complete()

AUTEUR : Expert Baccarat & Architecte Logiciel
DATE : 2025-06-29 - Version MULTIPROCESSING OPTIMISÉE
"""

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                          SECTION 1 : IMPORTS ET CONFIGURATION               ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

# ─────────────────────────────────────────────────────────────────────────────
# 1.1 IMPORTS STANDARD
# ─────────────────────────────────────────────────────────────────────────────
import json
import os
import glob
import multiprocessing
import concurrent.futures
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import traceback
import math
from collections import Counter, defaultdict
from scipy import stats
from scipy.stats import entropy
import warnings
warnings.filterwarnings('ignore')

# ─────────────────────────────────────────────────────────────────────────────
# 1.2 IMPORTS CONDITIONNELS POUR OPTIMISATIONS (technique VDIFF.py)
# ─────────────────────────────────────────────────────────────────────────────

# orjson : JSON ultra-rapide (10-20x plus rapide que json standard)
try:
    import orjson
    HAS_ORJSON = True
    print("🚀 orjson disponible - Performance JSON optimale")
except ImportError:
    HAS_ORJSON = False
    print("📦 orjson non disponible - Utilisation JSON standard")

# ijson : Streaming JSON pour gros fichiers
try:
    import ijson
    HAS_IJSON = True
    print("🔄 ijson disponible - Streaming JSON activé")
except ImportError:
    HAS_IJSON = False
    print("📦 ijson non disponible - Pas de streaming")

# Numba : JIT compilation pour calculs critiques
try:
    from numba import jit, prange, types
    from numba.typed import Dict as NumbaDict, List as NumbaList
    HAS_NUMBA = True
    print("⚡ Numba disponible - JIT compilation activée")
except ImportError:
    HAS_NUMBA = False
    print("📦 Numba non disponible - Calculs Python standard")

# ─────────────────────────────────────────────────────────────────────────────
# 1.3 CONFIGURATION MULTIPROCESSING RÉVOLUTIONNAIRE (8 cœurs, 28GB RAM)
# ─────────────────────────────────────────────────────────────────────────────
NB_CORES_SYSTEME = multiprocessing.cpu_count()
NB_CORES_UTILISES = min(8, NB_CORES_SYSTEME)

# Configuration RAM optimisée pour 28GB
RAM_DISPONIBLE_GB = 28
CHUNK_SIZE_OPTIMAL = 100_000  # Parties par chunk (optimisé pour 28GB)
BUFFER_SIZE_MB = 50  # Buffer de 50MB pour I/O optimisé
USE_AGGRESSIVE_CACHING = True  # Cache agressif avec 28GB disponibles

print(f"🚀 CONFIGURATION RÉVOLUTIONNAIRE DÉTECTÉE:")
print(f"   🖥️ Système: {NB_CORES_SYSTEME} cœurs - Utilisation: {NB_CORES_UTILISES} cœurs")
print(f"   💾 RAM disponible: {RAM_DISPONIBLE_GB}GB")
print(f"   📊 Chunk optimal: {CHUNK_SIZE_OPTIMAL:,} parties")
print(f"   ⚡ Cache agressif: {'✅' if USE_AGGRESSIVE_CACHING else '❌'}")
print(f"   🔧 Buffer I/O: {BUFFER_SIZE_MB}MB")

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                    SECTION 2 : OPTIMISATIONS PERFORMANCE                    ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

# ─────────────────────────────────────────────────────────────────────────────
# 2.1 FONCTIONS NUMBA JIT ULTRA-RAPIDES (technique VDIFF.py)
# ─────────────────────────────────────────────────────────────────────────────
if HAS_NUMBA:
    @jit(nopython=True, parallel=True, cache=True)
    def calcul_entropie_shannon_jit(sequence_array):
        """
        Calcul d'entropie Shannon ultra-rapide avec Numba JIT + VRAIES probabilités
        Technique VDIFF.py - Gain estimé : 10-50x par rapport à Python pur
        """
        # Probabilités réelles des 18 valeurs INDEX5 (ordre: 0-17)
        probs_reelles = np.array([
            0.08473, 0.08628,  # 0_A_BANKER, 1_A_BANKER
            0.06483, 0.06537,  # 0_B_BANKER, 1_B_BANKER
            0.07801, 0.07878,  # 0_C_BANKER, 1_C_BANKER
            0.08544, 0.08678,  # 0_A_PLAYER, 1_A_PLAYER
            0.07701, 0.07777,  # 0_B_PLAYER, 1_B_PLAYER
            0.05945, 0.06011,  # 0_C_PLAYER, 1_C_PLAYER
            0.01795, 0.01804,  # 0_A_TIE, 1_A_TIE
            0.01636, 0.01629,  # 0_B_TIE, 1_B_TIE
            0.01333, 0.01348   # 0_C_TIE, 1_C_TIE
        ], dtype=np.float64)

        # Calculer l'entropie avec les VRAIES probabilités
        entropie = 0.0
        for i in range(18):
            if probs_reelles[i] > 0:
                entropie -= probs_reelles[i] * math.log2(probs_reelles[i])
        return entropie

    @jit(nopython=True, parallel=True, cache=True)
    def traiter_parties_chunk_jit(parties_data, chunk_id):
        """
        Traitement ultra-rapide d'un chunk de parties avec Numba JIT
        """
        # Version simplifiée pour JIT - logique de base
        return len(parties_data)

    print("🚀 Fonctions Numba JIT compilées - Performance ULTRA-RAPIDE activée")

else:
    # ─────────────────────────────────────────────────────────────────────────────
    # 2.2 VERSIONS FALLBACK SANS NUMBA
    # ─────────────────────────────────────────────────────────────────────────────
    def calcul_entropie_shannon_jit(sequence_array):
        """Version fallback sans JIT avec VRAIES probabilités"""
        probs_reelles = np.array([
            0.08473, 0.08628, 0.06483, 0.06537, 0.07801, 0.07878,
            0.08544, 0.08678, 0.07701, 0.07777, 0.05945, 0.06011,
            0.01795, 0.01804, 0.01636, 0.01629, 0.01333, 0.01348
        ], dtype=np.float64)
        return -np.sum(probs_reelles * np.log2(probs_reelles + 1e-10))

    def traiter_parties_chunk_jit(parties_data, chunk_id):
        """Version fallback sans JIT"""
        return len(parties_data)

    print("📦 Fonctions fallback sans JIT - Performance standard")

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                   SECTION 3 : UTILITAIRES CHARGEMENT DATASET                ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

# ─────────────────────────────────────────────────────────────────────────────
# 3.1 FONCTION MULTIPROCESSING POUR TRAITEMENT CHUNKS
# ─────────────────────────────────────────────────────────────────────────────

def traiter_chunk_parties(chunk_parties: List[Dict], chunk_id: int) -> List:
    """
    Fonction globale pour traiter un chunk de parties en multiprocessing
    Technique VDIFF.py - Nécessaire pour ProcessPoolExecutor
    """
    try:

        parties_analysees = []

        for partie_data in chunk_parties:
            try:
                # Traiter la partie (logique extraite de _traiter_partie)
                partie_number = partie_data.get('partie_number', 0)
                burn_info = partie_data.get('burn_info', {})
                burn_cards_count = burn_info.get('burn_cards_count', 0)
                initial_sync_state = burn_info.get('initial_sync_state', 0)

                # Traiter les mains
                mains_data = partie_data.get('mains', [])
                mains_analysees = []

                for main_data in mains_data:
                    # Ignorer les mains dummy (main_number = None ou 0)
                    main_number = main_data.get('main_number')
                    if main_number is None or main_number == 0:
                        continue

                    main_analysee = MainAnalysee(
                        partie_number=partie_number,
                        main_number=main_number,
                        manche_pb_number=main_data.get('manche_pb_number'),
                        index1=main_data.get('index1', 0),
                        cards_count=main_data.get('cards_count', 0),
                        index2=main_data.get('index2', ''),
                        index3=main_data.get('index3', ''),
                        index5=f"{main_data.get('index1', 0)}_{main_data.get('index2', '')}_{main_data.get('index3', '')}"
                    )
                    mains_analysees.append(main_analysee)

                # Calculer les statistiques de la partie
                total_manches_pb = len([m for m in mains_analysees if m.index3 in ['PLAYER', 'BANKER']])
                total_ties = len([m for m in mains_analysees if m.index3 == 'TIE'])

                partie_analysee = PartieAnalysee(
                    partie_number=partie_number,
                    burn_cards_count=burn_cards_count,
                    initial_sync_state=initial_sync_state,
                    total_mains=len(mains_analysees),
                    mains=mains_analysees,
                    total_manches_pb=total_manches_pb,
                    total_ties=total_ties
                )

                parties_analysees.append(partie_analysee)

            except Exception as e:
                print(f"❌ Erreur partie {partie_data.get('partie_number', '?')} dans chunk {chunk_id}: {e}")
                continue

        print(f"✅ Chunk {chunk_id}: {len(parties_analysees)} parties traitées")
        return parties_analysees

    except Exception as e:
        print(f"❌ Erreur chunk {chunk_id}: {e}")
        return []

# ─────────────────────────────────────────────────────────────────────────────
# 3.2 DÉTECTION AUTOMATIQUE DU DATASET
# ─────────────────────────────────────────────────────────────────────────────
def detecter_dataset_le_plus_recent(compter_parties=False):
    """
    Détecte automatiquement le fichier JSON de dataset le plus récent
    et optionnellement compte le nombre total de parties qu'il contient.
    Technique adaptée de VDIFF.py pour chargement optimisé.

    Args:
        compter_parties: Si True, compte les parties (peut être lent pour gros datasets)
    """
    print("🔍 Détection dataset...")

    # Chercher tous les fichiers JSON de dataset dans le dossier courant et sous-dossiers
    patterns_dataset = [
        "dataset_baccarat_lupasco_*.json",
        "Nouveau dossier*/dataset_baccarat_lupasco_*.json",
        "*/dataset_baccarat_lupasco_*.json"
    ]

    fichiers_dataset = []
    for pattern in patterns_dataset:
        fichiers_dataset.extend(glob.glob(pattern))

    if not fichiers_dataset:
        print(f"❌ Aucun fichier dataset trouvé avec les patterns: {patterns_dataset}")
        return None, 0

    # Trier par taille (plus gros en premier) puis par date de modification
    fichiers_dataset.sort(key=lambda x: (os.path.getsize(x), os.path.getmtime(x)), reverse=True)

    print(f"📁 {len(fichiers_dataset)} fichier(s) dataset trouvé(s):")
    for fichier in fichiers_dataset:
        taille_gb = os.path.getsize(fichier) / (1024**3)
        print(f"   • {os.path.basename(fichier)} ({taille_gb:.2f} GB)")

    # Sélectionner le plus récent
    fichier_selectionne = fichiers_dataset[0]
    taille_gb = os.path.getsize(fichier_selectionne) / (1024**3)
    date_modification = datetime.fromtimestamp(os.path.getmtime(fichier_selectionne))

    print(f"\n✅ FICHIER LE PLUS RÉCENT SÉLECTIONNÉ:")
    print(f"   📄 Fichier: {os.path.basename(fichier_selectionne)}")
    print(f"   📊 Taille: {taille_gb:.2f} GB")
    print(f"   🕒 Modifié: {date_modification}")

    # Compter le nombre de parties si demandé
    nb_parties = 0
    if compter_parties:
        print(f"\n🔢 DÉCOMPTE AUTOMATIQUE DES PARTIES...")
        try:
            with open(fichier_selectionne, 'r', encoding='utf-8') as f:
                data = json.load(f)
                nb_parties = len(data.get('parties', []))

            print(f"✅ DÉCOMPTE TERMINÉ:")
            print(f"   🎯 Nombre total de parties: {nb_parties:,}")
            print(f"   📈 Données disponibles pour analyse complète")

        except Exception as e:
            print(f"❌ Erreur lors du décompte: {e}")
            return None, 0
    else:
        print(f"\n⚡ Décompte des parties ignoré pour accélérer le chargement")
        print(f"   📈 Le nombre de parties sera déterminé lors du chargement")

    return fichier_selectionne, nb_parties

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                        SECTION 4 : STRUCTURES DE DONNÉES                    ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

# ─────────────────────────────────────────────────────────────────────────────
# 4.1 STRUCTURES POUR ANALYSE FRACTALE
# ─────────────────────────────────────────────────────────────────────────────

@dataclass
class AnalyseFractale:
    """Structure pour stocker les résultats d'analyse fractale"""
    # Dimension fractale
    dimension_fractale: float

    # Exposant de Hurst
    exposant_hurst: float

    # Analyse DFA
    dfa_alpha: float
    dfa_fluctuation: List[float]
    dfa_scales: List[float]

    # Entropie
    entropie_shannon: float  # Entropie observée dans la séquence
    entropie_shannon_theorique: float  # Entropie théorique basée sur 100k parties
    entropie_uniforme: float  # Entropie assumant distribution uniforme (ancienne méthode)
    entropie_renyi: float

    # Complexité
    complexite_lempel_ziv: float

    # Auto-corrélation
    autocorrelation: List[float]

    # Patterns fractals
    patterns_detectes: Dict[str, int]

    # Mémoire longue
    memoire_longue: bool
    persistance: float

    # Multifractalité
    spectre_multifractal: Dict[str, float]

# ─────────────────────────────────────────────────────────────────────────────
# 4.2 STRUCTURES POUR DONNÉES BACCARAT
# ─────────────────────────────────────────────────────────────────────────────
@dataclass
class MainAnalysee:
    """Structure pour une main analysée avec toutes les informations essentielles"""
    partie_number: int
    main_number: int
    manche_pb_number: Optional[int]
    index1: int
    cards_count: int
    index2: str
    index3: str
    index5: str
    score_player: int = 0
    score_banker: int = 0
    timestamp: str = ""

@dataclass
class PartieAnalysee:
    """Structure pour une partie analysée"""
    partie_number: int
    burn_cards_count: int
    initial_sync_state: int
    total_mains: int
    mains: List[MainAnalysee]
    
    # Statistiques calculées
    total_manches_pb: int = 0
    total_ties: int = 0
    
# ─────────────────────────────────────────────────────────────────────────────
# 4.3 STRUCTURES POUR STATISTIQUES
# ─────────────────────────────────────────────────────────────────────────────
@dataclass
class StatistiquesGlobales:
    """Statistiques globales sur l'ensemble des données"""
    total_parties: int
    total_mains: int
    total_manches_pb: int
    total_ties: int

    # Répartition INDEX5
    repartition_index5: Dict[str, int]

    # Répartition par composants
    repartition_index1: Dict[int, int]
    repartition_index2: Dict[str, int]
    repartition_index3: Dict[str, int]

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                     SECTION 5 : CLASSE PRINCIPALE D'ANALYSE                 ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

# ─────────────────────────────────────────────────────────────────────────────
# 5.1 CLASSE ANALYSEUR PRINCIPAL
# ─────────────────────────────────────────────────────────────────────────────

class AnalyseurBaccaratLupasco:
    """
    Analyseur modulaire pour les données de baccarat Lupasco
    
    Cette classe sert de base stable pour tous types d'analyses.
    Elle charge les données, les structure et fournit une interface
    simple pour implémenter des analyses spécialisées.
    """
    
    def __init__(self, fichier_json: str = None):
        """
        Initialise l'analyseur

        Args:
            fichier_json: Chemin vers le fichier JSON de données.
                         Si None, détection automatique du dataset le plus récent.
        """
        if fichier_json is None:
            print("🔍 Aucun fichier spécifié - Détection automatique...")
            fichier_json, nb_parties = detecter_dataset_le_plus_recent(compter_parties=False)
            if fichier_json is None:
                raise ValueError("❌ Aucun dataset trouvé pour détection automatique")
            if nb_parties > 0:
                print(f"✅ Dataset détecté automatiquement: {nb_parties:,} parties")
            else:
                print(f"✅ Dataset détecté automatiquement (nombre de parties à déterminer)")

        self.fichier_json = fichier_json
        self.donnees_brutes: Optional[Dict] = None
        self.parties_analysees: List[PartieAnalysee] = []
        self.statistiques_globales: Optional[StatistiquesGlobales] = None

        print(f"🔍 Analyseur Baccarat Lupasco initialisé")
        print(f"📁 Fichier cible : {fichier_json}")
    
    # ─────────────────────────────────────────────────────────────────────────────
    # 5.2 MÉTHODES DE CHARGEMENT ET TRAITEMENT
    # ─────────────────────────────────────────────────────────────────────────────
    def charger_donnees(self) -> bool:
        """
        Charge et valide les données JSON
        
        Returns:
            bool: True si le chargement réussit, False sinon
        """
        try:
            print(f"\n📖 Chargement des données...")

            # Vérifier l'existence du fichier
            if not os.path.exists(self.fichier_json):
                print(f"❌ Erreur : Fichier {self.fichier_json} introuvable")
                return False

            # Afficher informations du fichier (technique VDIFF.py)
            taille_gb = os.path.getsize(self.fichier_json) / (1024**3)
            print(f"📊 Taille du fichier: {taille_gb:.2f} GB")

            # STRATÉGIE RÉVOLUTIONNAIRE : EXPLOITER LES 28GB DE RAM
            print("🔄 Chargement JSON en cours...")

            # Avec 28GB de RAM, on peut charger des fichiers beaucoup plus gros en mémoire
            if taille_gb <= 15.0:  # Jusqu'à 15GB on charge tout en RAM (garde 13GB libres)
                if HAS_ORJSON:
                    print(f"🚀 CHARGEMENT COMPLET EN RAM ({taille_gb:.2f}GB sur 28GB disponibles)")
                    print("⚡ Utilisation orjson ultra-rapide + multiprocessing 8 cœurs")
                    return self._charger_donnees_orjson_optimise_28gb()
                else:
                    print(f"📦 CHARGEMENT COMPLET EN RAM avec JSON standard")
                    return self._charger_donnees_standard()
            elif taille_gb > 15.0 and HAS_IJSON:
                # STREAMING seulement pour fichiers vraiment énormes (>15GB)
                print(f"🌊 Fichier très volumineux ({taille_gb:.2f}GB) - Utilisation streaming ijson")
                return self._charger_donnees_streaming()
            elif taille_gb > 2.0 and HAS_ORJSON:
                # orjson pour fichiers moyens
                print("🚀 Utilisation orjson pour performance optimale...")
                return self._charger_donnees_orjson()
            else:
                # JSON standard pour petits fichiers (<500MB)
                print("📦 Utilisation JSON standard...")
                return self._charger_donnees_standard()

            # Calcul des statistiques
            self._calculer_statistiques_globales()
            
            print(f"✅ Données chargées avec succès !")
            print(f"📊 {len(self.parties_analysees)} parties analysées")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON : {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur inattendue : {e}")
            traceback.print_exc()
            return False

    def _charger_donnees_streaming(self) -> bool:
        """
        Chargement streaming avec ijson pour très gros fichiers (>2GB)
        Mémoire constante - technique recommandée pour fichiers volumineux
        """
        try:
            print("🌊 Chargement streaming avec ijson...")

            # Initialiser les structures
            self.parties_analysees = []
            parties_count = 0

            # Streaming parser avec traitement par chunks optimisé pour 28GB RAM
            chunk_buffer = []
            chunk_size = CHUNK_SIZE_OPTIMAL // 100  # Chunks plus gros avec 28GB RAM disponibles

            with open(self.fichier_json, 'rb') as f:
                # Parser chaque partie individuellement
                parser = ijson.items(f, 'parties.item')

                for partie_data in parser:
                    parties_count += 1
                    chunk_buffer.append((partie_data, parties_count))

                    # Traiter le chunk quand il est plein
                    if len(chunk_buffer) >= chunk_size:
                        self._traiter_chunk_streaming(chunk_buffer)
                        chunk_buffer = []

                        if parties_count % 10000 == 0:
                            print(f"📊 Traitement streaming: {parties_count:,} parties...")

                # Traiter le dernier chunk s'il reste des données
                if chunk_buffer:
                    self._traiter_chunk_streaming(chunk_buffer)

            print(f"✅ Streaming terminé: {parties_count:,} parties traitées")
            print(f"📊 Parties analysées: {len(self.parties_analysees):,}")

            # Calculer les statistiques globales
            self._calculer_statistiques_globales()
            return True

        except Exception as e:
            print(f"❌ Erreur streaming ijson: {e}")
            return False

    def _traiter_chunk_streaming(self, chunk_buffer):
        """
        Traite un chunk de parties en streaming optimisé
        Technique inspirée d'analyseur_transitions_index5.py
        """
        for partie_data, partie_number in chunk_buffer:
            # Traiter la partie immédiatement (pas de stockage en mémoire)
            partie_analysee = self._traiter_partie_streaming(partie_data, partie_number)
            if partie_analysee:
                self.parties_analysees.append(partie_analysee)

    def _charger_donnees_orjson_optimise_28gb(self) -> bool:
        """
        Chargement RÉVOLUTIONNAIRE optimisé pour 28GB RAM + 8 cœurs
        Technique inspirée d'analyseur_transitions_index5.py
        """
        try:
            print("🚀 CHARGEMENT RÉVOLUTIONNAIRE 28GB RAM + 8 CŒURS")
            print("=" * 60)

            # Chargement ultra-rapide avec orjson
            print("⚡ Chargement orjson ultra-rapide...")
            with open(self.fichier_json, 'rb') as f:
                self.donnees_brutes = orjson.loads(f.read())

            parties = self.donnees_brutes.get('parties', [])
            taille_gb = len(str(self.donnees_brutes)) / (1024**3)

            print(f"✅ Données chargées en RAM:")
            print(f"   📊 Parties: {len(parties):,}")
            print(f"   💾 Utilisation RAM: ~{taille_gb:.2f}GB sur 28GB disponibles")
            print(f"   🚀 Traitement multiprocessing sur {NB_CORES_UTILISES} cœurs")

            if not self._valider_structure_json():
                return False

            # Traitement multiprocessing optimisé pour gros volumes
            return self._traiter_donnees_multiprocessing_optimise(parties)

        except Exception as e:
            print(f"❌ Erreur chargement optimisé 28GB: {e}")
            # Fallback sur orjson standard
            return self._charger_donnees_orjson()

    def _charger_donnees_orjson(self) -> bool:
        """
        Chargement ultra-rapide avec orjson pour fichiers moyens (500MB-2GB)
        """
        try:
            print("🚀 Chargement orjson ultra-rapide...")

            with open(self.fichier_json, 'rb') as f:
                self.donnees_brutes = orjson.loads(f.read())

            # Validation et traitement
            parties = self.donnees_brutes.get('parties', [])
            print(f"✅ orjson chargé: {len(parties):,} parties")

            if not self._valider_structure_json():
                return False

            return self._traiter_donnees()

        except Exception as e:
            print(f"❌ Erreur orjson: {e}")
            return False

    def _charger_donnees_standard(self) -> bool:
        """
        Chargement JSON standard pour petits fichiers (<500MB)
        """
        try:
            print("📦 Chargement JSON standard...")

            with open(self.fichier_json, 'r', encoding='utf-8') as f:
                self.donnees_brutes = json.load(f)

            # Validation et traitement
            parties = self.donnees_brutes.get('parties', [])
            print(f"✅ JSON standard chargé: {len(parties):,} parties")

            if not self._valider_structure_json():
                return False

            return self._traiter_donnees()

        except Exception as e:
            print(f"❌ Erreur JSON standard: {e}")
            return False

    def _traiter_partie_streaming(self, partie_data: Dict, partie_number: int) -> Optional[PartieAnalysee]:
        """
        Traite une partie individuellement lors du streaming
        Optimisé pour mémoire constante
        """
        try:
            mains_data = partie_data.get('mains', [])
            if not mains_data:
                return None

            # Traiter les mains avec délimitation correcte
            mains_analysees = []
            for main_data in mains_data:
                # CORRECTION: Utiliser 'main_number' comme dans le chargement standard
                main_number = main_data.get('main_number')

                # Exclure les mains dummy (main_number = None ou 0)
                if main_number is None or main_number == 0:
                    continue

                # Extraire les données essentielles avec les bons noms de champs
                main_analysee = MainAnalysee(
                    partie_number=partie_number,
                    main_number=main_number,
                    manche_pb_number=main_data.get('manche_pb_number'),
                    index1=main_data.get('index1', 0),
                    cards_count=main_data.get('cards_count', 0),
                    index2=main_data.get('index2', ''),
                    index3=main_data.get('index3', ''),
                    index5=main_data.get('index5', ''),
                    score_player=main_data.get('score_player', 0),
                    score_banker=main_data.get('score_banker', 0),
                    timestamp=main_data.get('timestamp', '')
                )
                mains_analysees.append(main_analysee)

            if not mains_analysees:
                return None

            # Extraire les informations de burn et statistiques
            burn_info = partie_data.get('burn_info', {})
            burn_cards_count = burn_info.get('burn_cards_count', 0) if burn_info else 0

            statistiques = partie_data.get('statistiques', {})
            initial_sync_state = statistiques.get('initial_sync_state', 0) if statistiques else 0

            # Créer la partie analysée avec tous les champs requis
            partie_analysee = PartieAnalysee(
                partie_number=partie_number,
                burn_cards_count=burn_cards_count,
                initial_sync_state=initial_sync_state,
                total_mains=len(mains_analysees),
                mains=mains_analysees,
                total_manches_pb=sum(1 for m in mains_analysees if m.manche_pb_number is not None),
                total_ties=sum(1 for m in mains_analysees if m.index3 == 'TIE')
            )

            return partie_analysee

        except Exception as e:
            print(f"❌ Erreur traitement partie {partie_number}: {e}")
            return None
    
    def _valider_structure_json(self) -> bool:
        """Valide la structure du JSON chargé"""
        try:
            # Vérifier les clés principales
            if 'parties' not in self.donnees_brutes:
                print("❌ Erreur : Clé 'parties' manquante dans le JSON")
                return False
            
            if 'metadata' not in self.donnees_brutes:
                print("❌ Erreur : Clé 'metadata' manquante dans le JSON")
                return False
            
            parties = self.donnees_brutes['parties']
            if not isinstance(parties, list) or len(parties) == 0:
                print("❌ Erreur : Aucune partie trouvée dans les données")
                return False
            
            print(f"✅ Structure JSON valide - {len(parties)} parties détectées")
            return True
            
        except Exception as e:
            print(f"❌ Erreur validation : {e}")
            return False
    
    def _traiter_donnees(self) -> bool:
        """Traite les données brutes avec multiprocessing optimisé (technique VDIFF.py)"""
        try:
            print(f"🔄 Traitement des données avec multiprocessing...")

            parties_brutes = self.donnees_brutes['parties']
            nb_parties = len(parties_brutes)

            # Décider si utiliser multiprocessing (seuil: 1000 parties)
            if nb_parties >= 1000 and NB_CORES_UTILISES > 1:
                print(f"⚡ Traitement multiprocessing: {nb_parties:,} parties sur {NB_CORES_UTILISES} cœurs")
                return self._traiter_donnees_multiprocessing(parties_brutes)
            else:
                print(f"📦 Traitement séquentiel: {nb_parties:,} parties")
                return self._traiter_donnees_sequentiel(parties_brutes)

        except Exception as e:
            print(f"❌ Erreur traitement : {e}")
            traceback.print_exc()
            return False

    def _traiter_donnees_sequentiel(self, parties_brutes: List[Dict]) -> bool:
        """Traitement séquentiel classique"""
        for partie_data in parties_brutes:
            partie_analysee = self._traiter_partie(partie_data)
            if partie_analysee:
                self.parties_analysees.append(partie_analysee)

        print(f"✅ {len(self.parties_analysees)} parties traitées (séquentiel)")
        self._afficher_delimitations_parties()
        return True

    def _traiter_donnees_multiprocessing(self, parties_brutes: List[Dict]) -> bool:
        """Traitement multiprocessing optimisé pour 8 cœurs"""
        try:
            nb_parties = len(parties_brutes)

            # Calculer la taille optimale des chunks pour 28GB RAM (technique révolutionnaire)
            chunk_size = max(CHUNK_SIZE_OPTIMAL // (NB_CORES_UTILISES * 4), 100)
            chunks = [parties_brutes[i:i+chunk_size] for i in range(0, nb_parties, chunk_size)]

            print(f"📊 Configuration multiprocessing:")
            print(f"   🔢 Parties: {nb_parties:,}")
            print(f"   🧩 Chunks: {len(chunks)} (taille: {chunk_size})")
            print(f"   🖥️ Cœurs: {NB_CORES_UTILISES}")

            # Traitement parallèle avec ProcessPoolExecutor
            with concurrent.futures.ProcessPoolExecutor(max_workers=NB_CORES_UTILISES) as executor:
                # Soumettre les chunks
                futures = [executor.submit(traiter_chunk_parties, chunk, i) for i, chunk in enumerate(chunks)]

                # Collecter les résultats
                for future in concurrent.futures.as_completed(futures):
                    try:
                        parties_chunk = future.result()
                        if parties_chunk:
                            self.parties_analysees.extend(parties_chunk)
                    except Exception as e:
                        print(f"❌ Erreur chunk: {e}")

            print(f"✅ {len(self.parties_analysees)} parties traitées (multiprocessing)")
            self._afficher_delimitations_parties()
            return True

        except Exception as e:
            print(f"❌ Erreur multiprocessing: {e}")
            # Fallback sur traitement séquentiel
            print("🔄 Fallback sur traitement séquentiel...")
            return self._traiter_donnees_sequentiel(parties_brutes)

    def _traiter_donnees_multiprocessing_optimise(self, parties_brutes: List[Dict]) -> bool:
        """
        Traitement multiprocessing RÉVOLUTIONNAIRE pour 28GB RAM + 8 cœurs
        Technique inspirée d'analyseur_transitions_index5.py avec chunks optimaux
        """
        try:
            nb_parties = len(parties_brutes)

            print(f"🚀 TRAITEMENT MULTIPROCESSING RÉVOLUTIONNAIRE")
            print(f"=" * 60)
            print(f"📊 Parties à traiter: {nb_parties:,}")
            print(f"💾 Données en RAM: ~{len(str(parties_brutes)) / (1024**3):.2f}GB")

            # Calculer la taille optimale des chunks pour 28GB RAM + 8 cœurs
            # Technique d'analyseur_transitions_index5.py
            chunk_size_optimal = max(CHUNK_SIZE_OPTIMAL // NB_CORES_UTILISES, 1000)
            chunks = [parties_brutes[i:i+chunk_size_optimal] for i in range(0, nb_parties, chunk_size_optimal)]

            print(f"🔧 Configuration optimisée:")
            print(f"   🧩 Chunks: {len(chunks)} (taille: {chunk_size_optimal:,})")
            print(f"   🖥️ Cœurs: {NB_CORES_UTILISES}/{NB_CORES_SYSTEME}")
            print(f"   ⚡ RAM par chunk: ~{chunk_size_optimal * 50 / 1024:.1f}MB")

            # Traitement parallèle révolutionnaire
            with concurrent.futures.ProcessPoolExecutor(max_workers=NB_CORES_UTILISES) as executor:
                # Soumettre tous les chunks simultanément
                futures = [executor.submit(traiter_chunk_parties, chunk, i) for i, chunk in enumerate(chunks)]

                # Collecter les résultats avec suivi de progression
                chunks_completed = 0
                for future in concurrent.futures.as_completed(futures):
                    try:
                        chunk_result = future.result()
                        self.parties_analysees.extend(chunk_result)
                        chunks_completed += 1

                        if chunks_completed % max(1, len(chunks) // 10) == 0:
                            progress = (chunks_completed / len(chunks)) * 100
                            print(f"   ⚡ Progression: {progress:.1f}% ({chunks_completed}/{len(chunks)} chunks)")

                    except Exception as e:
                        print(f"❌ Erreur chunk: {e}")

            print(f"✅ TRAITEMENT RÉVOLUTIONNAIRE TERMINÉ")
            print(f"   📊 Parties traitées: {len(self.parties_analysees):,}")
            print(f"   🚀 Performance: {NB_CORES_UTILISES} cœurs utilisés simultanément")

            self._afficher_delimitations_parties()
            return True

        except Exception as e:
            print(f"❌ Erreur traitement révolutionnaire: {e}")
            # Fallback sur multiprocessing standard
            print("🔄 Fallback sur multiprocessing standard...")
            return self._traiter_donnees_multiprocessing(parties_brutes)

    def _afficher_delimitations_parties(self):
        """Affiche les délimitations des parties (excluant main dummy)"""
        delimitations = self.obtenir_delimitations_parties()
        print(f"📊 Délimitations des parties (excluant main dummy):")
        for partie_num, delim in list(delimitations.items())[:3]:  # Afficher les 3 premières
            print(f"   Partie {partie_num}: Main {delim['main_debut']} → {delim['main_fin']} ({delim['total_mains']} mains)")
        if len(delimitations) > 3:
            print(f"   ... et {len(delimitations) - 3} autres parties")
    
    def _traiter_partie(self, partie_data: Dict) -> Optional[PartieAnalysee]:
        """Traite une partie individuelle"""
        try:
            # Extraire les informations de base
            partie_number = partie_data.get('partie_number', 0)
            burn_info = partie_data.get('burn_info', {})
            burn_cards_count = burn_info.get('burn_cards_count', 0)
            initial_sync_state = burn_info.get('initial_sync_state', 0)
            
            # Traiter les mains
            mains_data = partie_data.get('mains', [])
            mains_analysees = []
            
            for main_data in mains_data:
                main_analysee = self._traiter_main(main_data, partie_number)
                if main_analysee:
                    mains_analysees.append(main_analysee)
            
            # Calculer les statistiques de la partie
            total_manches_pb = len([m for m in mains_analysees if m.index3 in ['PLAYER', 'BANKER']])
            total_ties = len([m for m in mains_analysees if m.index3 == 'TIE'])
            
            return PartieAnalysee(
                partie_number=partie_number,
                burn_cards_count=burn_cards_count,
                initial_sync_state=initial_sync_state,
                total_mains=len(mains_analysees),
                mains=mains_analysees,
                total_manches_pb=total_manches_pb,
                total_ties=total_ties
            )
            
        except Exception as e:
            print(f"❌ Erreur traitement partie {partie_data.get('partie_number', '?')} : {e}")
            return None
    
    def _traiter_main(self, main_data: Dict, partie_number: int) -> Optional[MainAnalysee]:
        """Traite une main individuelle"""
        try:
            # Ignorer les mains dummy (main_number = None)
            main_number = main_data.get('main_number')
            if main_number is None:
                return None
            
            return MainAnalysee(
                partie_number=partie_number,
                main_number=main_number,
                manche_pb_number=main_data.get('manche_pb_number'),
                index1=main_data.get('index1', 0),
                cards_count=main_data.get('cards_count', 0),
                index2=main_data.get('index2', ''),
                index3=main_data.get('index3', ''),
                index5=main_data.get('index5', ''),
                score_player=main_data.get('score_player', 0),
                score_banker=main_data.get('score_banker', 0),
                timestamp=main_data.get('timestamp', '')
            )
            
        except Exception as e:
            print(f"❌ Erreur traitement main : {e}")
            return None
    
    def _calculer_statistiques_globales(self):
        """Calcule les statistiques globales sur toutes les données"""
        try:
            # Compteurs globaux
            total_parties = len(self.parties_analysees)
            total_mains = sum(p.total_mains for p in self.parties_analysees)
            total_manches_pb = sum(p.total_manches_pb for p in self.parties_analysees)
            total_ties = sum(p.total_ties for p in self.parties_analysees)
            
            # Répartitions
            repartition_index5 = {}
            repartition_index1 = {0: 0, 1: 0}
            repartition_index2 = {'A': 0, 'B': 0, 'C': 0}
            repartition_index3 = {'PLAYER': 0, 'BANKER': 0, 'TIE': 0}
            
            # Parcourir toutes les mains
            for partie in self.parties_analysees:
                for main in partie.mains:
                    # INDEX5
                    if main.index5:
                        repartition_index5[main.index5] = repartition_index5.get(main.index5, 0) + 1
                    
                    # INDEX1
                    if main.index1 in repartition_index1:
                        repartition_index1[main.index1] += 1
                    
                    # INDEX2
                    if main.index2 in repartition_index2:
                        repartition_index2[main.index2] += 1
                    
                    # INDEX3
                    if main.index3 in repartition_index3:
                        repartition_index3[main.index3] += 1
            
            self.statistiques_globales = StatistiquesGlobales(
                total_parties=total_parties,
                total_mains=total_mains,
                total_manches_pb=total_manches_pb,
                total_ties=total_ties,
                repartition_index5=repartition_index5,
                repartition_index1=repartition_index1,
                repartition_index2=repartition_index2,
                repartition_index3=repartition_index3
            )
            
        except Exception as e:
            print(f"❌ Erreur calcul statistiques : {e}")
    
    def obtenir_index5_toutes_mains(self, exclure_main_dummy: bool = True) -> List[Tuple[int, int, str]]:
        """
        Obtient la liste de tous les INDEX5 pour chaque main de chaque partie

        Args:
            exclure_main_dummy: Si True, exclut la main dummy (main 0) de l'analyse

        Returns:
            List[Tuple[int, int, str]]: Liste de (partie_number, main_number, index5)
        """
        index5_list = []

        for partie in self.parties_analysees:
            for main in partie.mains:
                # Filtrer la main dummy si demandé
                if exclure_main_dummy and (main.main_number is None or main.main_number == 0):
                    continue
                index5_list.append((partie.partie_number, main.main_number, main.index5))

        return index5_list
    
    def obtenir_mains_par_partie(self, partie_number: int) -> List[MainAnalysee]:
        """
        Obtient toutes les mains d'une partie spécifique
        
        Args:
            partie_number: Numéro de la partie
            
        Returns:
            List[MainAnalysee]: Liste des mains de la partie
        """
        for partie in self.parties_analysees:
            if partie.partie_number == partie_number:
                return partie.mains
        return []
    
    # ─────────────────────────────────────────────────────────────────────────────
    # 5.3 MÉTHODES D'EXTRACTION ET FILTRAGE
    # ─────────────────────────────────────────────────────────────────────────────
    
    def filtrer_mains(self, **criteres) -> List[MainAnalysee]:
        """
        Filtre les mains selon des critères spécifiques
        
        Args:
            **criteres: Critères de filtrage (index1, index2, index3, etc.)
            
        Returns:
            List[MainAnalysee]: Mains correspondant aux critères
        """
        mains_filtrees = []
        
        for partie in self.parties_analysees:
            for main in partie.mains:
                correspond = True
                
                for cle, valeur in criteres.items():
                    if hasattr(main, cle) and getattr(main, cle) != valeur:
                        correspond = False
                        break
                
                if correspond:
                    mains_filtrees.append(main)
        
        return mains_filtrees

    def obtenir_parties_disponibles(self) -> List[int]:
        """Retourne la liste des numéros de parties disponibles"""
        try:
            if not self.parties_analysees:
                return []

            return [partie.partie_number for partie in self.parties_analysees]

        except Exception as e:
            print(f"❌ Erreur obtention parties disponibles: {e}")
            return []

    def obtenir_delimitations_parties(self) -> Dict[int, Dict[str, int]]:
        """
        Retourne les délimitations de chaque partie (main 1 à dernière main)
        Exclut la main dummy (main 0) pour l'analyse fractal

        Returns:
            Dict[int, Dict[str, int]]: {partie_number: {'main_debut': 1, 'main_fin': X, 'total_mains': Y}}
        """
        try:
            delimitations = {}

            for partie in self.parties_analysees:
                if partie.mains:
                    # Filtrer les mains valides (exclure main dummy et main_number None)
                    mains_valides = [m for m in partie.mains if m.main_number is not None and m.main_number > 0]

                    if mains_valides:
                        main_debut = min(m.main_number for m in mains_valides)
                        main_fin = max(m.main_number for m in mains_valides)
                        total_mains = len(mains_valides)

                        delimitations[partie.partie_number] = {
                            'main_debut': main_debut,
                            'main_fin': main_fin,
                            'total_mains': total_mains,
                            'mains_valides': sorted([m.main_number for m in mains_valides])
                        }

            return delimitations

        except Exception as e:
            print(f"❌ Erreur obtention délimitations: {e}")
            return {}

    def obtenir_index5_par_partie(self, partie_number: int, exclure_main_dummy: bool = True) -> List[str]:
        """
        Obtient la séquence INDEX5 d'une partie spécifique

        Args:
            partie_number: Numéro de la partie
            exclure_main_dummy: Si True, exclut la main dummy (main 0) de l'analyse
        """
        try:
            for partie in self.parties_analysees:
                if partie.partie_number == partie_number:
                    if exclure_main_dummy:
                        # Filtrer les mains valides (exclure main dummy)
                        mains_valides = [m for m in partie.mains
                                       if m.main_number is not None and m.main_number > 0 and m.index5]
                        return [main.index5 for main in sorted(mains_valides, key=lambda x: x.main_number)]
                    else:
                        return [main.index5 for main in partie.mains if main.index5]

            print(f"⚠️ Partie {partie_number} non trouvée")
            return []

        except Exception as e:
            print(f"❌ Erreur obtention INDEX5 partie {partie_number}: {e}")
            return []

    def afficher_resume(self):
        """Affiche un résumé des données chargées"""
        try:
            if not self.statistiques_globales:
                print("❌ Aucune donnée à afficher")
                return

            stats = self.statistiques_globales
            print(f"\n📊 RÉSUMÉ DES DONNÉES")
            print(f"-" * 30)
            print(f"Parties analysées : {stats.total_parties}")
            print(f"Total mains : {stats.total_mains}")
            print(f"Total manches P/B : {stats.total_manches_pb}")
            print(f"Total TIE : {stats.total_ties}")
            print(f"Moyenne mains/partie : {stats.total_mains/stats.total_parties:.1f}")

            # Afficher les délimitations (excluant main dummy)
            delimitations = self.obtenir_delimitations_parties()
            print(f"\n🎯 DÉLIMITATIONS DES PARTIES (excluant main dummy)")
            print(f"-" * 50)
            for partie_num, delim in list(delimitations.items())[:5]:  # Afficher les 5 premières
                print(f"Partie {partie_num:3d}: Main {delim['main_debut']:3d} → {delim['main_fin']:3d} ({delim['total_mains']:3d} mains)")
            if len(delimitations) > 5:
                print(f"... et {len(delimitations) - 5} autres parties")

        except Exception as e:
            print(f"❌ Erreur affichage résumé: {e}")

    # ╔══════════════════════════════════════════════════════════════════════════════╗
    # ║                    SECTION 6 : MÉTHODES D'ANALYSE FRACTALE                  ║
    # ╚══════════════════════════════════════════════════════════════════════════════╝

    # ─────────────────────────────────────────────────────────────────────────────
    # 6.1 ANALYSE FRACTALE PRINCIPALE
    # ─────────────────────────────────────────────────────────────────────────────

    def analyser_fractales_partie(self, partie_number: int) -> Optional[AnalyseFractale]:
        """
        Effectue une analyse fractale complète d'une partie

        Args:
            partie_number: Numéro de la partie à analyser

        Returns:
            AnalyseFractale: Résultats de l'analyse fractale
        """
        try:
            # Obtenir la séquence INDEX5 de la partie
            index5_sequence = self.obtenir_index5_par_partie(partie_number)

            if len(index5_sequence) < 10:
                print(f"⚠️ Partie {partie_number} trop courte pour analyse fractale ({len(index5_sequence)} mains)")
                return None

            print(f"🔬 Analyse fractale de la partie {partie_number} ({len(index5_sequence)} mains)")

            # Convertir INDEX5 en séries numériques pour l'analyse
            series_numeriques = self._convertir_index5_en_series(index5_sequence)

            # Effectuer toutes les analyses fractales
            analyse = AnalyseFractale(
                dimension_fractale=self._calculer_dimension_fractale(series_numeriques['index1']),
                exposant_hurst=self._calculer_exposant_hurst(series_numeriques['index1']),
                dfa_alpha=0.0,
                dfa_fluctuation=[],
                dfa_scales=[],
                entropie_shannon=self._calculer_entropie_shannon(index5_sequence),
                entropie_shannon_theorique=self._calculer_entropie_shannon_theorique(index5_sequence),
                entropie_uniforme=np.log2(len(set(index5_sequence))) if index5_sequence else 0.0,
                entropie_renyi=self._calculer_entropie_renyi(index5_sequence),
                complexite_lempel_ziv=self._calculer_complexite_lempel_ziv(index5_sequence),
                autocorrelation=self._calculer_autocorrelation(series_numeriques['index1']),
                patterns_detectes=self._detecter_patterns_fractals(index5_sequence),
                memoire_longue=False,
                persistance=0.0,
                spectre_multifractal={}
            )

            # Analyse DFA
            dfa_result = self._analyser_dfa(series_numeriques['index1'])
            analyse.dfa_alpha = dfa_result['alpha']
            analyse.dfa_fluctuation = dfa_result['fluctuation']
            analyse.dfa_scales = dfa_result['scales']

            # Déterminer mémoire longue et persistance
            analyse.memoire_longue = analyse.exposant_hurst > 0.5
            analyse.persistance = analyse.exposant_hurst

            # Analyse multifractale
            analyse.spectre_multifractal = self._analyser_multifractal(series_numeriques['index1'])

            return analyse

        except Exception as e:
            print(f"❌ Erreur analyse fractale partie {partie_number}: {e}")
            traceback.print_exc()
            return None

    def _convertir_index5_en_series(self, index5_sequence: List[str]) -> Dict[str, List[float]]:
        """Convertit les INDEX5 en séries numériques pour l'analyse"""
        series = {
            'index1': [],
            'index2_num': [],
            'index3_num': [],
            'combined': []
        }

        # Mapping pour INDEX2
        index2_map = {'A': 1, 'B': 2, 'C': 3}
        # Mapping pour INDEX3
        index3_map = {'BANKER': 1, 'PLAYER': 2, 'TIE': 3}

        for index5 in index5_sequence:
            if '_' in index5:
                parts = index5.split('_')
                if len(parts) == 3:
                    index1 = int(parts[0])
                    index2 = parts[1]
                    index3 = parts[2]

                    series['index1'].append(float(index1))
                    series['index2_num'].append(float(index2_map.get(index2, 0)))
                    series['index3_num'].append(float(index3_map.get(index3, 0)))

                    # Série combinée (INDEX1 * 100 + INDEX2 * 10 + INDEX3)
                    combined = index1 * 100 + index2_map.get(index2, 0) * 10 + index3_map.get(index3, 0)
                    series['combined'].append(float(combined))

        return series

    def _calculer_dimension_fractale(self, serie: List[float]) -> float:
        """Calcule la dimension fractale par box-counting"""
        try:
            if len(serie) < 4:
                return 1.0

            # Normaliser la série
            serie_norm = np.array(serie)
            serie_norm = (serie_norm - np.min(serie_norm)) / (np.max(serie_norm) - np.min(serie_norm) + 1e-10)

            # Méthode box-counting simplifiée
            scales = np.logspace(0.5, np.log10(len(serie)/4), 10)
            counts = []

            for scale in scales:
                box_size = int(max(1, scale))
                n_boxes = 0

                for i in range(0, len(serie_norm), box_size):
                    box_data = serie_norm[i:i+box_size]
                    if len(box_data) > 0 and np.std(box_data) > 1e-10:
                        n_boxes += 1

                counts.append(max(1, n_boxes))

            # Régression linéaire pour obtenir la dimension
            log_scales = np.log(scales)
            log_counts = np.log(counts)

            if len(log_scales) > 1 and np.std(log_scales) > 0:
                slope, _, _, _, _ = stats.linregress(log_scales, log_counts)
                return abs(slope)

            return 1.0

        except Exception as e:
            print(f"Erreur calcul dimension fractale: {e}")
            return 1.0

    def _calculer_exposant_hurst(self, serie: List[float]) -> float:
        """Calcule l'exposant de Hurst par analyse R/S"""
        try:
            if len(serie) < 10:
                return 0.5

            serie = np.array(serie)
            n = len(serie)

            # Calcul de la moyenne
            mean_serie = np.mean(serie)

            # Écarts cumulés
            cumulative_devs = np.cumsum(serie - mean_serie)

            # Range (étendue)
            R = np.max(cumulative_devs) - np.min(cumulative_devs)

            # Standard deviation
            S = np.std(serie)

            if S == 0:
                return 0.5

            # R/S ratio
            rs_ratio = R / S

            # Exposant de Hurst approximatif
            if rs_ratio > 0:
                hurst = np.log(rs_ratio) / np.log(n)
                return max(0.0, min(1.0, hurst))

            return 0.5

        except Exception as e:
            print(f"Erreur calcul exposant Hurst: {e}")
            return 0.5

    def _analyser_dfa(self, serie: List[float]) -> Dict[str, Any]:
        """Analyse DFA (Detrended Fluctuation Analysis)"""
        try:
            if len(serie) < 16:
                return {'alpha': 0.5, 'fluctuation': [], 'scales': []}

            serie = np.array(serie)
            n = len(serie)

            # Intégration de la série
            y = np.cumsum(serie - np.mean(serie))

            # Échelles d'analyse
            scales = np.unique(np.logspace(0.5, np.log10(n//4), 10).astype(int))
            fluctuations = []

            for scale in scales:
                # Diviser en segments
                n_segments = n // scale

                if n_segments < 2:
                    continue

                # Calcul des fluctuations
                local_fluctuations = []

                for i in range(n_segments):
                    start = i * scale
                    end = start + scale
                    segment = y[start:end]

                    # Détrendre par régression linéaire
                    x = np.arange(len(segment))
                    if len(segment) > 1:
                        coeffs = np.polyfit(x, segment, 1)
                        trend = np.polyval(coeffs, x)
                        detrended = segment - trend
                        local_fluctuations.append(np.sqrt(np.mean(detrended**2)))

                if local_fluctuations:
                    fluctuations.append(np.mean(local_fluctuations))
                else:
                    fluctuations.append(0.0)

            # Calcul de l'exposant alpha
            if len(scales) > 1 and len(fluctuations) > 1:
                log_scales = np.log(scales[:len(fluctuations)])
                log_fluctuations = np.log(np.array(fluctuations) + 1e-10)

                if np.std(log_scales) > 0:
                    slope, _, _, _, _ = stats.linregress(log_scales, log_fluctuations)
                    alpha = slope
                else:
                    alpha = 0.5
            else:
                alpha = 0.5

            return {
                'alpha': max(0.0, min(2.0, alpha)),
                'fluctuation': fluctuations,
                'scales': scales[:len(fluctuations)].tolist()
            }

        except Exception as e:
            print(f"Erreur analyse DFA: {e}")
            return {'alpha': 0.5, 'fluctuation': [], 'scales': []}

    def _calculer_entropie_shannon(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie de Shannon avec les probabilités réelles
        basées sur l'analyse de 100,000 parties (6,632,137 mains)
        """
        try:
            if not sequence:
                return 0.0

            # Probabilités réelles observées sur 100,000 parties
            # Source: rapport_proportions_index5_20250629_054237.txt
            probabilites_reelles = {
                '0_A_BANKER': 0.08526, '1_A_BANKER': 0.08621,
                '0_A_PLAYER': 0.08518, '1_A_PLAYER': 0.08625,
                '0_B_BANKER': 0.06451, '1_B_BANKER': 0.06535,
                '0_B_PLAYER': 0.07704, '1_B_PLAYER': 0.07785,
                '0_C_BANKER': 0.07805, '1_C_BANKER': 0.07889,
                '0_C_PLAYER': 0.05966, '1_C_PLAYER': 0.06044,
                '0_A_TIE': 0.01778, '1_A_TIE': 0.01794,
                '0_B_TIE': 0.01631, '1_B_TIE': 0.01647,
                '0_C_TIE': 0.01333, '1_C_TIE': 0.01348
            }

            # Compter les occurrences dans la séquence
            counts = Counter(sequence)
            total = len(sequence)

            # Calculer l'entropie avec les probabilités réelles
            entropy_val = 0.0

            # Méthode 1: Entropie basée sur les fréquences observées dans la séquence
            for valeur, count in counts.items():
                if count > 0:
                    p_observed = count / total
                    entropy_val -= p_observed * np.log2(p_observed)

            # Méthode 2: Entropie théorique basée sur les probabilités réelles
            # (commentée pour garder la compatibilité, mais plus précise)
            # entropy_theorique = 0.0
            # for valeur in set(sequence):
            #     p_reel = probabilites_reelles.get(valeur, 1/18)
            #     if p_reel > 0:
            #         entropy_theorique -= p_reel * np.log2(p_reel)
            #
            # # Ajuster selon la composition de la séquence
            # facteur_ajustement = self._calculer_facteur_ajustement(sequence, probabilites_reelles)
            # entropy_val = entropy_theorique * facteur_ajustement

            return entropy_val

        except Exception as e:
            print(f"Erreur calcul entropie Shannon: {e}")
            return 0.0

    def _calculer_entropie_shannon_theorique(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie de Shannon théorique basée sur les probabilités réelles
        du dataset de 100,000 parties
        """
        try:
            if not sequence:
                return 0.0

            # Probabilités réelles observées sur 100,000 parties
            probabilites_reelles = {
                '0_A_BANKER': 0.08526, '1_A_BANKER': 0.08621,
                '0_A_PLAYER': 0.08518, '1_A_PLAYER': 0.08625,
                '0_B_BANKER': 0.06451, '1_B_BANKER': 0.06535,
                '0_B_PLAYER': 0.07704, '1_B_PLAYER': 0.07785,
                '0_C_BANKER': 0.07805, '1_C_BANKER': 0.07889,
                '0_C_PLAYER': 0.05966, '1_C_PLAYER': 0.06044,
                '0_A_TIE': 0.01778, '1_A_TIE': 0.01794,
                '0_B_TIE': 0.01631, '1_B_TIE': 0.01647,
                '0_C_TIE': 0.01333, '1_C_TIE': 0.01348
            }

            # Calculer l'entropie théorique maximale
            entropy_theorique = 0.0
            for p_reel in probabilites_reelles.values():
                if p_reel > 0:
                    entropy_theorique -= p_reel * np.log2(p_reel)

            return entropy_theorique

        except Exception as e:
            print(f"Erreur calcul entropie Shannon théorique: {e}")
            return 0.0

    def _calculer_entropie_renyi(self, sequence: List[str], alpha: float = 2.0) -> float:
        """Calcule l'entropie de Rényi"""
        try:
            if not sequence:
                return 0.0

            counts = Counter(sequence)
            total = len(sequence)

            if alpha == 1.0:
                return self._calculer_entropie_shannon(sequence)

            # Entropie de Rényi
            sum_p_alpha = sum((count/total)**alpha for count in counts.values())

            if sum_p_alpha > 0:
                return (1/(1-alpha)) * np.log2(sum_p_alpha)

            return 0.0

        except Exception as e:
            print(f"Erreur calcul entropie Rényi: {e}")
            return 0.0

    def _calculer_complexite_lempel_ziv(self, sequence: List[str]) -> float:
        """Calcule la complexité de Lempel-Ziv"""
        try:
            if not sequence:
                return 0.0

            # Convertir en chaîne
            string = ''.join(sequence)
            n = len(string)

            if n <= 1:
                return 1.0

            # Algorithme LZ77 simplifié
            complexity = 0
            i = 0

            while i < n:
                # Chercher la plus longue correspondance
                max_length = 0

                for j in range(i):
                    length = 0
                    while (i + length < n and
                           j + length < i and
                           string[i + length] == string[j + length]):
                        length += 1
                    max_length = max(max_length, length)

                # Avancer d'au moins 1 caractère
                i += max(1, max_length)
                complexity += 1

            # Normaliser par la longueur théorique maximale
            max_complexity = n / np.log2(n) if n > 1 else 1
            return complexity / max_complexity

        except Exception as e:
            print(f"Erreur calcul complexité Lempel-Ziv: {e}")
            return 0.0

    def _calculer_autocorrelation(self, serie: List[float], max_lag: int = 20) -> List[float]:
        """Calcule l'autocorrélation"""
        try:
            if len(serie) < 2:
                return [1.0]

            serie = np.array(serie)
            n = len(serie)
            max_lag = min(max_lag, n//2)

            # Centrer la série
            serie_centered = serie - np.mean(serie)

            autocorr = []
            for lag in range(max_lag + 1):
                if lag == 0:
                    autocorr.append(1.0)
                else:
                    if n - lag > 0:
                        corr = np.corrcoef(serie_centered[:-lag], serie_centered[lag:])[0, 1]
                        autocorr.append(corr if not np.isnan(corr) else 0.0)
                    else:
                        autocorr.append(0.0)

            return autocorr

        except Exception as e:
            print(f"Erreur calcul autocorrélation: {e}")
            return [1.0]

    def _detecter_patterns_fractals(self, sequence: List[str]) -> Dict[str, int]:
        """Détecte les patterns fractals dans la séquence"""
        try:
            patterns = {}

            # Patterns de longueur 2 à 5
            for length in range(2, min(6, len(sequence))):
                pattern_counts = Counter()

                for i in range(len(sequence) - length + 1):
                    pattern = tuple(sequence[i:i+length])
                    pattern_counts[pattern] += 1

                # Garder les patterns qui se répètent
                for pattern, count in pattern_counts.items():
                    if count > 1:
                        pattern_str = '_'.join(pattern)
                        patterns[f"L{length}_{pattern_str}"] = count

            # Auto-similarité : chercher des patterns qui se répètent à différentes échelles
            self_similar_patterns = 0
            for i in range(len(sequence) // 2):
                if i < len(sequence) - i - 1:
                    if sequence[i] == sequence[len(sequence) - i - 1]:
                        self_similar_patterns += 1

            patterns['auto_similarite'] = self_similar_patterns

            return patterns

        except Exception as e:
            print(f"Erreur détection patterns: {e}")
            return {}

    def _analyser_multifractal(self, serie: List[float]) -> Dict[str, float]:
        """Analyse multifractale simplifiée"""
        try:
            if len(serie) < 16:
                return {'largeur_spectre': 0.0, 'asymetrie': 0.0}

            serie = np.array(serie)

            # Calcul de différents moments
            moments = [0.5, 1.0, 1.5, 2.0, 2.5]
            exposants = []

            for q in moments:
                # Calcul simplifié de l'exposant de Hurst généralisé
                try:
                    # Méthode des moments
                    n = len(serie)
                    scales = np.unique(np.logspace(0.5, np.log10(n//4), 8).astype(int))

                    fluctuations = []
                    for scale in scales:
                        segments = []
                        for i in range(0, n - scale + 1, scale):
                            segment = serie[i:i+scale]
                            if len(segment) == scale:
                                segments.append(np.std(segment))

                        if segments:
                            if q != 1.0:
                                moment = np.mean(np.array(segments)**q)**(1/q)
                            else:
                                moment = np.exp(np.mean(np.log(np.array(segments) + 1e-10)))
                            fluctuations.append(moment)

                    if len(fluctuations) > 1 and len(scales) > 1:
                        log_scales = np.log(scales[:len(fluctuations)])
                        log_fluctuations = np.log(np.array(fluctuations) + 1e-10)

                        if np.std(log_scales) > 0:
                            slope, _, _, _, _ = stats.linregress(log_scales, log_fluctuations)
                            exposants.append(slope)
                        else:
                            exposants.append(0.5)
                    else:
                        exposants.append(0.5)

                except:
                    exposants.append(0.5)

            # Calcul des caractéristiques du spectre multifractal
            if len(exposants) > 1:
                largeur_spectre = max(exposants) - min(exposants)
                asymetrie = np.mean(exposants) - np.median(exposants)
            else:
                largeur_spectre = 0.0
                asymetrie = 0.0

            return {
                'largeur_spectre': largeur_spectre,
                'asymetrie': asymetrie,
                'exposants': exposants
            }

        except Exception as e:
            print(f"Erreur analyse multifractale: {e}")
            return {'largeur_spectre': 0.0, 'asymetrie': 0.0}

    # ============================================================================
    # MÉTHODES DE PRÉDICTION FRACTALE
    # ============================================================================

    def predire_prochaine_main_fractal(self, partie_number: int, methode: str = "hurst") -> Dict[str, Any]:
        """
        Prédit la prochaine main en utilisant l'analyse fractale

        Args:
            partie_number: Numéro de la partie
            methode: Méthode de prédiction ("hurst", "dfa", "patterns", "multifractal")

        Returns:
            Dict contenant la prédiction et la confiance
        """
        try:
            # Obtenir l'analyse fractale
            analyse = self.analyser_fractales_partie(partie_number)
            if not analyse:
                return {"prediction": None, "confiance": 0.0, "methode": methode}

            # Obtenir la séquence INDEX5
            index5_sequence = self.obtenir_index5_par_partie(partie_number)

            if methode == "hurst":
                return self._predire_par_hurst(index5_sequence, analyse)
            elif methode == "dfa":
                return self._predire_par_dfa(index5_sequence, analyse)
            elif methode == "patterns":
                return self._predire_par_patterns(index5_sequence, analyse)
            elif methode == "multifractal":
                return self._predire_par_multifractal(index5_sequence, analyse)
            else:
                # Méthode combinée
                return self._predire_combine(index5_sequence, analyse)

        except Exception as e:
            print(f"Erreur prédiction fractale: {e}")
            return {"prediction": None, "confiance": 0.0, "methode": methode}

    def _predire_par_hurst(self, sequence: List[str], analyse: AnalyseFractale) -> Dict[str, Any]:
        """Prédiction basée sur l'exposant de Hurst"""
        try:
            if not sequence:
                return {"prediction": None, "confiance": 0.0, "methode": "hurst"}

            # Analyser la tendance basée sur Hurst
            hurst = analyse.exposant_hurst
            derniere_main = sequence[-1]

            # Extraire les composants
            parts = derniere_main.split('_')
            if len(parts) != 3:
                return {"prediction": None, "confiance": 0.0, "methode": "hurst"}

            index1_actuel = int(parts[0])
            index2_actuel = parts[1]
            index3_actuel = parts[2]

            # Prédiction basée sur la persistance/anti-persistance
            if hurst > 0.6:  # Forte persistance
                # Tendance à continuer
                confiance = min(0.8, (hurst - 0.5) * 2)

                # Prédire la continuation de la tendance INDEX1
                if len(sequence) >= 3:
                    recent_index1 = [int(s.split('_')[0]) for s in sequence[-3:]]
                    if sum(recent_index1) >= 2:  # Majorité de 1
                        prediction_index1 = 1
                    else:
                        prediction_index1 = 0
                else:
                    prediction_index1 = index1_actuel

            elif hurst < 0.4:  # Anti-persistance
                # Tendance à s'inverser
                confiance = min(0.7, (0.5 - hurst) * 2)
                prediction_index1 = 1 - index1_actuel

            else:  # Mouvement brownien
                confiance = 0.3
                prediction_index1 = index1_actuel

            # Appliquer les règles de transition INDEX1
            prediction_index1 = self._appliquer_regles_transition(
                index1_actuel, index2_actuel, prediction_index1
            )

            # Prédire INDEX2 et INDEX3 basé sur les patterns récents
            index2_pred = self._predire_index2_patterns(sequence)
            index3_pred = self._predire_index3_patterns(sequence)

            prediction = f"{prediction_index1}_{index2_pred}_{index3_pred}"

            return {
                "prediction": prediction,
                "confiance": confiance,
                "methode": "hurst",
                "hurst_value": hurst,
                "interpretation": "persistant" if hurst > 0.5 else "anti-persistant"
            }

        except Exception as e:
            print(f"Erreur prédiction Hurst: {e}")
            return {"prediction": None, "confiance": 0.0, "methode": "hurst"}

    def _appliquer_regles_transition(self, index1_actuel: int, index2_actuel: str, prediction_index1: int) -> int:
        """Applique les règles de transition INDEX1 selon INDEX2"""
        if index2_actuel == 'C':
            # C flip l'état
            return 1 - index1_actuel
        else:
            # A et B préservent l'état
            return index1_actuel

    def _predire_index2_patterns(self, sequence: List[str]) -> str:
        """Prédit INDEX2 basé sur les patterns récents"""
        try:
            if len(sequence) < 3:
                return 'A'  # Valeur par défaut

            # Analyser les patterns INDEX2 récents
            recent_index2 = [s.split('_')[1] for s in sequence[-5:]]

            # Compter les occurrences
            counts = Counter(recent_index2)

            # Prédire le plus fréquent ou suivre une séquence
            if len(set(recent_index2)) == 1:
                # Même valeur répétée, prédire changement
                current = recent_index2[-1]
                options = ['A', 'B', 'C']
                options.remove(current)
                return np.random.choice(options)
            else:
                # Prédire le plus fréquent
                return counts.most_common(1)[0][0]

        except:
            return 'A'

    def _predire_index3_patterns(self, sequence: List[str]) -> str:
        """Prédit INDEX3 basé sur les patterns récents"""
        try:
            if len(sequence) < 3:
                return 'BANKER'  # Valeur par défaut

            # Analyser les patterns INDEX3 récents
            recent_index3 = [s.split('_')[2] for s in sequence[-5:]]

            # Compter les occurrences
            counts = Counter(recent_index3)

            # Prédire basé sur la fréquence et les cycles
            most_common = counts.most_common(1)[0][0]

            # Vérifier s'il y a un cycle
            if len(recent_index3) >= 3:
                if recent_index3[-1] == recent_index3[-3]:
                    # Possible cycle de longueur 2
                    if recent_index3[-2] != recent_index3[-1]:
                        return recent_index3[-2]

            return most_common

        except:
            return 'BANKER'

    def _predire_par_dfa(self, sequence: List[str], analyse: AnalyseFractale) -> Dict[str, Any]:
        """Prédiction basée sur l'analyse DFA"""
        try:
            alpha = analyse.dfa_alpha

            # Interpréter l'exposant DFA
            if alpha > 1.0:
                # Corrélations long-terme fortes
                confiance = min(0.8, (alpha - 0.5) * 0.6)
                interpretation = "correlations_fortes"
            elif alpha < 0.5:
                # Anti-corrélations
                confiance = min(0.7, (0.5 - alpha) * 1.4)
                interpretation = "anti_correlations"
            else:
                # Bruit blanc
                confiance = 0.3
                interpretation = "aleatoire"

            # Utiliser la même logique que Hurst mais avec DFA
            prediction = self._predire_par_hurst(sequence, analyse)
            prediction["methode"] = "dfa"
            prediction["dfa_alpha"] = alpha
            prediction["interpretation"] = interpretation
            prediction["confiance"] = confiance

            return prediction

        except Exception as e:
            print(f"Erreur prédiction DFA: {e}")
            return {"prediction": None, "confiance": 0.0, "methode": "dfa"}

    def _predire_par_patterns(self, sequence: List[str], analyse: AnalyseFractale) -> Dict[str, Any]:
        """Prédiction basée sur les patterns détectés"""
        try:
            patterns = analyse.patterns_detectes

            if not patterns:
                return {"prediction": None, "confiance": 0.0, "methode": "patterns"}

            # Chercher le pattern le plus récent qui se répète
            best_prediction = None
            best_confiance = 0.0

            for pattern_name, count in patterns.items():
                if pattern_name.startswith('L') and count > 1:
                    # Extraire la longueur du pattern
                    try:
                        length = int(pattern_name.split('_')[0][1:])
                        if length <= len(sequence):
                            # Vérifier si la fin de la séquence correspond au début du pattern
                            pattern_elements = pattern_name.split('_')[1:]

                            if len(pattern_elements) == length and length > 1:
                                recent_sequence = sequence[-(length-1):]

                                # Vérifier la correspondance
                                match = True
                                for i, element in enumerate(pattern_elements[:-1]):
                                    if i < len(recent_sequence):
                                        if recent_sequence[i] != element:
                                            match = False
                                            break

                                if match:
                                    prediction = pattern_elements[-1]
                                    confiance = min(0.8, count / len(sequence) * 2)

                                    if confiance > best_confiance:
                                        best_prediction = prediction
                                        best_confiance = confiance
                    except:
                        continue

            return {
                "prediction": best_prediction,
                "confiance": best_confiance,
                "methode": "patterns",
                "patterns_detectes": len(patterns)
            }

        except Exception as e:
            print(f"Erreur prédiction patterns: {e}")
            return {"prediction": None, "confiance": 0.0, "methode": "patterns"}

    def _predire_par_multifractal(self, sequence: List[str], analyse: AnalyseFractale) -> Dict[str, Any]:
        """Prédiction basée sur l'analyse multifractale"""
        try:
            spectre = analyse.spectre_multifractal

            if not spectre:
                return {"prediction": None, "confiance": 0.0, "methode": "multifractal"}

            largeur = spectre.get('largeur_spectre', 0.0)
            asymetrie = spectre.get('asymetrie', 0.0)

            # Interpréter le spectre multifractal
            if largeur > 0.3:
                # Forte multifractalité
                confiance = min(0.7, largeur)
                interpretation = "multifractal_fort"
            else:
                # Faible multifractalité
                confiance = 0.4
                interpretation = "multifractal_faible"

            # Utiliser l'asymétrie pour orienter la prédiction
            prediction = self._predire_par_hurst(sequence, analyse)
            prediction["methode"] = "multifractal"
            prediction["largeur_spectre"] = largeur
            prediction["asymetrie"] = asymetrie
            prediction["interpretation"] = interpretation
            prediction["confiance"] = confiance

            return prediction

        except Exception as e:
            print(f"Erreur prédiction multifractale: {e}")
            return {"prediction": None, "confiance": 0.0, "methode": "multifractal"}

    def _predire_combine(self, sequence: List[str], analyse: AnalyseFractale) -> Dict[str, Any]:
        """Prédiction combinée utilisant toutes les méthodes"""
        try:
            # Obtenir toutes les prédictions
            pred_hurst = self._predire_par_hurst(sequence, analyse)
            pred_dfa = self._predire_par_dfa(sequence, analyse)
            pred_patterns = self._predire_par_patterns(sequence, analyse)
            pred_multifractal = self._predire_par_multifractal(sequence, analyse)

            predictions = [pred_hurst, pred_dfa, pred_patterns, pred_multifractal]

            # Filtrer les prédictions valides
            valid_predictions = [p for p in predictions if p["prediction"] is not None]

            if not valid_predictions:
                return {"prediction": None, "confiance": 0.0, "methode": "combine"}

            # Pondérer par la confiance
            weighted_votes = {}
            total_weight = 0.0

            for pred in valid_predictions:
                prediction = pred["prediction"]
                confiance = pred["confiance"]

                if prediction in weighted_votes:
                    weighted_votes[prediction] += confiance
                else:
                    weighted_votes[prediction] = confiance

                total_weight += confiance

            # Choisir la prédiction avec le plus grand poids
            if weighted_votes:
                best_prediction = max(weighted_votes.items(), key=lambda x: x[1])
                final_prediction = best_prediction[0]
                final_confiance = best_prediction[1] / total_weight if total_weight > 0 else 0.0
            else:
                final_prediction = None
                final_confiance = 0.0

            return {
                "prediction": final_prediction,
                "confiance": final_confiance,
                "methode": "combine",
                "predictions_individuelles": {
                    "hurst": pred_hurst,
                    "dfa": pred_dfa,
                    "patterns": pred_patterns,
                    "multifractal": pred_multifractal
                }
            }

        except Exception as e:
            print(f"Erreur prédiction combinée: {e}")
            return {"prediction": None, "confiance": 0.0, "methode": "combine"}

    # ╔══════════════════════════════════════════════════════════════════════════════╗
    # ║                      SECTION 7 : MÉTHODES DE REPORTING                      ║
    # ╚══════════════════════════════════════════════════════════════════════════════╝

    # ─────────────────────────────────────────────────────────────────────────────
    # 7.1 GÉNÉRATION DE RAPPORTS
    # ─────────────────────────────────────────────────────────────────────────────

    def generer_rapport_fractal_complet_ancien(self, partie_number: int, nom_fichier: str = None) -> str:
        """Génère un rapport complet d'analyse fractale"""
        try:
            if nom_fichier is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                nom_fichier = f"rapport_fractal_partie_{partie_number}_{timestamp}.txt"

            # Effectuer l'analyse
            analyse = self.analyser_fractales_partie(partie_number)
            if not analyse:
                return "Erreur: Impossible d'analyser la partie"

            # Obtenir les données de base
            index5_sequence = self.obtenir_index5_par_partie(partie_number)

            # Générer les prédictions
            predictions = {}
            for methode in ["hurst", "dfa", "patterns", "multifractal", "combine"]:
                predictions[methode] = self.predire_prochaine_main_fractal(partie_number, methode)

            # Créer le rapport
            rapport = []
            rapport.append("=" * 80)
            rapport.append("RAPPORT D'ANALYSE FRACTALE BACCARAT LUPASCO")
            rapport.append("=" * 80)
            rapport.append(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            rapport.append(f"Partie analysée : {partie_number}")
            rapport.append(f"Nombre de mains : {len(index5_sequence)}")
            rapport.append(f"Source : {self.fichier_json}")
            rapport.append("")

            # Section 1: Caractéristiques Fractales
            rapport.append("🔬 CARACTÉRISTIQUES FRACTALES")
            rapport.append("-" * 50)
            rapport.append(f"Dimension fractale : {analyse.dimension_fractale:.4f}")
            rapport.append(f"Exposant de Hurst : {analyse.exposant_hurst:.4f}")

            if analyse.exposant_hurst > 0.6:
                rapport.append("  → Série PERSISTANTE (tendances long-terme)")
            elif analyse.exposant_hurst < 0.4:
                rapport.append("  → Série ANTI-PERSISTANTE (retours à la moyenne)")
            else:
                rapport.append("  → Série ALÉATOIRE (mouvement brownien)")

            rapport.append(f"Exposant DFA (α) : {analyse.dfa_alpha:.4f}")

            if analyse.dfa_alpha > 1.0:
                rapport.append("  → Corrélations long-terme FORTES")
            elif analyse.dfa_alpha < 0.5:
                rapport.append("  → Anti-corrélations présentes")
            else:
                rapport.append("  → Comportement proche du bruit blanc")

            rapport.append("")

            # Section 2: Entropie et Complexité
            rapport.append("📊 ENTROPIE ET COMPLEXITÉ")
            rapport.append("-" * 50)

            # Comparaison des méthodes d'entropie de Shannon
            rapport.append("🔍 ENTROPIE DE SHANNON - COMPARAISON DES MÉTHODES:")
            rapport.append(f"  • Entropie observée (séquence) : {analyse.entropie_shannon:.4f}")
            rapport.append(f"  • Entropie théorique (100k parties) : {analyse.entropie_shannon_theorique:.4f}")
            rapport.append(f"  • Entropie uniforme (ancienne méthode) : {analyse.entropie_uniforme:.4f}")

            # Calcul des écarts
            ecart_theorique = abs(analyse.entropie_shannon - analyse.entropie_shannon_theorique)
            ecart_uniforme = abs(analyse.entropie_shannon - analyse.entropie_uniforme)
            rapport.append(f"  • Écart vs théorique : {ecart_theorique:.4f}")
            rapport.append(f"  • Écart vs uniforme : {ecart_uniforme:.4f}")

            # Amélioration apportée
            if ecart_theorique < ecart_uniforme:
                rapport.append("  ✅ CORRECTION VALIDÉE: Méthode théorique plus précise")
            else:
                rapport.append("  ⚠️  Méthode uniforme semble plus proche pour cette séquence")

            rapport.append("")
            rapport.append(f"Entropie de Rényi : {analyse.entropie_renyi:.4f}")
            rapport.append(f"Complexité Lempel-Ziv : {analyse.complexite_lempel_ziv:.4f}")

            # Interprétation de l'entropie
            max_entropy = analyse.entropie_shannon_theorique  # Utiliser l'entropie théorique comme référence
            entropy_ratio = analyse.entropie_shannon / max_entropy

            if entropy_ratio > 0.8:
                rapport.append("  → Série très IMPRÉVISIBLE (haute entropie)")
            elif entropy_ratio > 0.6:
                rapport.append("  → Série modérément prévisible")
            else:
                rapport.append("  → Série avec patterns DÉTECTABLES (basse entropie)")

            rapport.append("")

            # Section 3: Analyse Multifractale
            rapport.append("🌀 ANALYSE MULTIFRACTALE")
            rapport.append("-" * 50)

            if analyse.spectre_multifractal:
                largeur = analyse.spectre_multifractal.get('largeur_spectre', 0.0)
                asymetrie = analyse.spectre_multifractal.get('asymetrie', 0.0)

                rapport.append(f"Largeur du spectre : {largeur:.4f}")
                rapport.append(f"Asymétrie : {asymetrie:.4f}")

                if largeur > 0.3:
                    rapport.append("  → Structure MULTIFRACTALE détectée")
                else:
                    rapport.append("  → Structure monofractale ou faiblement multifractale")
            else:
                rapport.append("Analyse multifractale non disponible")

            rapport.append("")

            # Section 4: Patterns Détectés
            rapport.append("🔍 PATTERNS FRACTALS DÉTECTÉS")
            rapport.append("-" * 50)

            if analyse.patterns_detectes:
                for pattern, count in sorted(analyse.patterns_detectes.items(),
                                           key=lambda x: x[1], reverse=True)[:10]:
                    if pattern != 'auto_similarite':
                        rapport.append(f"{pattern} : {count} occurrences")

                auto_sim = analyse.patterns_detectes.get('auto_similarite', 0)
                rapport.append(f"Auto-similarité : {auto_sim} éléments")
            else:
                rapport.append("Aucun pattern fractal significatif détecté")

            rapport.append("")

            # Section 5: Prédictions
            rapport.append("🎯 PRÉDICTIONS FRACTALES")
            rapport.append("-" * 50)

            for methode, pred in predictions.items():
                if pred["prediction"]:
                    confiance_pct = pred["confiance"] * 100
                    rapport.append(f"{methode.upper():12} : {pred['prediction']} (confiance: {confiance_pct:.1f}%)")
                else:
                    rapport.append(f"{methode.upper():12} : Prédiction impossible")

            # Recommandation finale
            rapport.append("")
            rapport.append("💡 RECOMMANDATION FINALE")
            rapport.append("-" * 50)

            best_pred = max(predictions.values(), key=lambda x: x["confiance"])
            if best_pred["prediction"] and best_pred["confiance"] > 0.5:
                rapport.append(f"Prédiction recommandée : {best_pred['prediction']}")
                rapport.append(f"Méthode : {best_pred['methode']}")
                rapport.append(f"Confiance : {best_pred['confiance']*100:.1f}%")
            else:
                rapport.append("Aucune prédiction fiable disponible")
                rapport.append("La série présente un comportement trop aléatoire")

            # Section 6: Données Détaillées
            rapport.append("")
            rapport.append("📋 DONNÉES DÉTAILLÉES DE LA SÉQUENCE")
            rapport.append("-" * 50)

            # Afficher la séquence INDEX5 complète
            rapport.append("Séquence INDEX5 complète :")
            for i, index5 in enumerate(index5_sequence, 1):
                rapport.append(f"Main {i:2d}: {index5}")

            rapport.append("")

            # Section 7: Autocorrélations
            rapport.append("📈 ANALYSE D'AUTOCORRÉLATION")
            rapport.append("-" * 50)

            if len(analyse.autocorrelation) > 1:
                rapport.append("Coefficients d'autocorrélation (premiers 10 lags) :")
                for i, corr in enumerate(analyse.autocorrelation[:10]):
                    rapport.append(f"Lag {i:2d}: {corr:7.4f}")

                # Interprétation
                if analyse.autocorrelation[1] > 0.3:
                    rapport.append("  → Forte autocorrélation positive (persistance)")
                elif analyse.autocorrelation[1] < -0.3:
                    rapport.append("  → Forte autocorrélation négative (alternance)")
                else:
                    rapport.append("  → Autocorrélation faible (comportement aléatoire)")

            rapport.append("")

            # Section 8: Analyse DFA Détaillée
            if analyse.dfa_scales and analyse.dfa_fluctuation:
                rapport.append("🔬 ANALYSE DFA DÉTAILLÉE")
                rapport.append("-" * 50)
                rapport.append("Échelles et fluctuations DFA :")

                for i, (scale, fluct) in enumerate(zip(analyse.dfa_scales[:8], analyse.dfa_fluctuation[:8])):
                    rapport.append(f"Échelle {scale:3d}: Fluctuation {fluct:.6f}")

            rapport.append("")
            rapport.append("=" * 80)

            # Sauvegarder le rapport
            contenu_rapport = "\n".join(rapport)

            with open(nom_fichier, 'w', encoding='utf-8') as f:
                f.write(contenu_rapport)

            print(f"✅ Rapport fractal généré : {nom_fichier}")
            return nom_fichier

        except Exception as e:
            print(f"❌ Erreur génération rapport fractal: {e}")
            traceback.print_exc()
            return ""



    def analyser_toutes_parties_fractales(self) -> Dict[int, AnalyseFractale]:
        """Analyse fractale de toutes les parties du dataset"""
        try:
            print("🔬 Analyse fractale de toutes les parties...")

            resultats = {}
            parties_disponibles = self.obtenir_parties_disponibles()

            for partie_num in parties_disponibles:
                print(f"Analyse partie {partie_num}...")
                analyse = self.analyser_fractales_partie(partie_num)
                if analyse:
                    resultats[partie_num] = analyse

            print(f"✅ Analyse fractale terminée pour {len(resultats)} parties")
            return resultats

        except Exception as e:
            print(f"❌ Erreur analyse toutes parties: {e}")
            return {}

    def comparer_parties_fractales(self, parties: List[int] = None) -> str:
        """Compare les caractéristiques fractales entre plusieurs parties"""
        try:
            if parties is None:
                parties = self.obtenir_parties_disponibles()

            if len(parties) < 2:
                return "Erreur: Au moins 2 parties nécessaires pour la comparaison"

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nom_fichier = f"comparaison_fractales_{timestamp}.txt"

            rapport = []
            rapport.append("=" * 80)
            rapport.append("COMPARAISON FRACTALE ENTRE PARTIES")
            rapport.append("=" * 80)
            rapport.append(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            rapport.append(f"Parties comparées : {parties}")
            rapport.append("")

            # Analyser chaque partie
            analyses = {}
            for partie in parties:
                analyse = self.analyser_fractales_partie(partie)
                if analyse:
                    analyses[partie] = analyse

            if not analyses:
                return "Erreur: Aucune partie analysable"

            # Tableau comparatif
            rapport.append("📊 TABLEAU COMPARATIF")
            rapport.append("-" * 80)

            # En-têtes
            header = f"{'Partie':<8} {'Hurst':<8} {'DFA α':<8} {'Dim.Frac':<10} {'Entropie':<10} {'Complex.':<10}"
            rapport.append(header)
            rapport.append("-" * 80)

            # Données
            for partie, analyse in analyses.items():
                ligne = f"{partie:<8} {analyse.exposant_hurst:<8.3f} {analyse.dfa_alpha:<8.3f} "
                ligne += f"{analyse.dimension_fractale:<10.3f} {analyse.entropie_shannon:<10.3f} "
                ligne += f"{analyse.complexite_lempel_ziv:<10.3f}"
                rapport.append(ligne)

            rapport.append("")

            # Statistiques comparatives
            rapport.append("📈 STATISTIQUES COMPARATIVES")
            rapport.append("-" * 50)

            # Calculer moyennes et écarts-types
            hurst_values = [a.exposant_hurst for a in analyses.values()]
            dfa_values = [a.dfa_alpha for a in analyses.values()]
            dim_values = [a.dimension_fractale for a in analyses.values()]
            entropy_values = [a.entropie_shannon for a in analyses.values()]

            rapport.append(f"Exposant de Hurst - Moyenne: {np.mean(hurst_values):.3f}, Écart-type: {np.std(hurst_values):.3f}")
            rapport.append(f"DFA Alpha - Moyenne: {np.mean(dfa_values):.3f}, Écart-type: {np.std(dfa_values):.3f}")
            rapport.append(f"Dimension fractale - Moyenne: {np.mean(dim_values):.3f}, Écart-type: {np.std(dim_values):.3f}")
            rapport.append(f"Entropie Shannon - Moyenne: {np.mean(entropy_values):.3f}, Écart-type: {np.std(entropy_values):.3f}")

            rapport.append("")

            # Identification des parties remarquables
            rapport.append("🎯 PARTIES REMARQUABLES")
            rapport.append("-" * 50)

            # Partie la plus persistante
            max_hurst_partie = max(analyses.items(), key=lambda x: x[1].exposant_hurst)
            rapport.append(f"Plus persistante : Partie {max_hurst_partie[0]} (Hurst = {max_hurst_partie[1].exposant_hurst:.3f})")

            # Partie la plus complexe
            max_entropy_partie = max(analyses.items(), key=lambda x: x[1].entropie_shannon)
            rapport.append(f"Plus complexe : Partie {max_entropy_partie[0]} (Entropie = {max_entropy_partie[1].entropie_shannon:.3f})")

            # Partie la plus fractale
            max_dim_partie = max(analyses.items(), key=lambda x: x[1].dimension_fractale)
            rapport.append(f"Plus fractale : Partie {max_dim_partie[0]} (Dimension = {max_dim_partie[1].dimension_fractale:.3f})")

            rapport.append("")
            rapport.append("=" * 80)

            # Sauvegarder
            contenu = "\n".join(rapport)
            with open(nom_fichier, 'w', encoding='utf-8') as f:
                f.write(contenu)

            print(f"✅ Comparaison fractale générée : {nom_fichier}")
            return nom_fichier

        except Exception as e:
            print(f"❌ Erreur comparaison fractales: {e}")
            return ""

    def executer_analyse_basique(self):
        """Exécute une analyse basique des données et affiche les résultats"""
        if not self.statistiques_globales:
            print("❌ Aucune donnée à analyser")
            return

        print(f"\n" + "="*60)
        print(f"📊 ANALYSE BASIQUE DES DONNÉES BACCARAT LUPASCO")
        print(f"="*60)

        stats = self.statistiques_globales

        # Statistiques générales
        print(f"\n📈 STATISTIQUES GÉNÉRALES :")
        print(f"Total parties : {stats.total_parties}")
        print(f"Total mains : {stats.total_mains}")
        print(f"Total manches P/B : {stats.total_manches_pb}")
        print(f"Total TIE : {stats.total_ties}")
        print(f"Moyenne mains/partie : {stats.total_mains/stats.total_parties:.1f}")

        # Répartition INDEX1
        print(f"\n🔄 RÉPARTITION INDEX1 (SYNC/DESYNC) :")
        total_index1 = sum(stats.repartition_index1.values())
        for index1, count in stats.repartition_index1.items():
            etat = "SYNC" if index1 == 0 else "DESYNC"
            pourcentage = (count/total_index1)*100 if total_index1 > 0 else 0
            print(f"{etat} ({index1}) : {count} ({pourcentage:.1f}%)")

        # Répartition INDEX2
        print(f"\n🎯 RÉPARTITION INDEX2 (CARTES) :")
        total_index2 = sum(stats.repartition_index2.values())
        for index2, count in stats.repartition_index2.items():
            cartes = {"A": "4 cartes", "B": "6 cartes", "C": "5 cartes"}.get(index2, index2)
            pourcentage = (count/total_index2)*100 if total_index2 > 0 else 0
            print(f"{index2} ({cartes}) : {count} ({pourcentage:.1f}%)")

        # Répartition INDEX3
        print(f"\n🏆 RÉPARTITION INDEX3 (RÉSULTATS) :")
        total_index3 = sum(stats.repartition_index3.values())
        for index3, count in stats.repartition_index3.items():
            pourcentage = (count/total_index3)*100 if total_index3 > 0 else 0
            print(f"{index3} : {count} ({pourcentage:.1f}%)")

        # Top 10 INDEX5
        print(f"\n🔝 TOP 10 INDEX5 LES PLUS FRÉQUENTS :")
        index5_tries = sorted(stats.repartition_index5.items(), key=lambda x: x[1], reverse=True)
        for i, (index5, count) in enumerate(index5_tries[:10], 1):
            pourcentage = (count/stats.total_mains)*100
            print(f"{i:2d}. {index5:12s} : {count:3d} ({pourcentage:.1f}%)")

        print(f"\n" + "="*60)

    def executer_analyse_personnalisee(self, fonction_analyse):
        """
        Exécute une fonction d'analyse personnalisée

        Args:
            fonction_analyse: Fonction qui prend l'analyseur en paramètre
        """
        try:
            print(f"\n🔬 Exécution d'une analyse personnalisée...")
            fonction_analyse(self)
        except Exception as e:
            print(f"❌ Erreur dans l'analyse personnalisée : {e}")
            traceback.print_exc()

    def exporter_index5_txt(self, nom_fichier: str = "index5_export.txt"):
        """
        Génère un fichier TXT vide avec en-tête minimal

        Args:
            nom_fichier: Nom du fichier TXT de sortie
        """
        try:
            with open(nom_fichier, 'w', encoding='utf-8') as txtfile:
                # En-tête minimal du fichier
                txtfile.write("EXPORT INDEX5 - ANALYSEUR BACCARAT LUPASCO\n")
                txtfile.write("=" * 60 + "\n")
                txtfile.write(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                txtfile.write(f"Source : {self.fichier_json}\n")
                txtfile.write("=" * 60 + "\n\n")

                # Le fichier reste vide de données comme demandé
                txtfile.write("# Fichier prêt pour analyses spécialisées\n")
                txtfile.write("# Aucune donnée affichée par défaut\n")

            print(f"✅ Export TXT terminé : {nom_fichier}")

        except Exception as e:
            print(f"❌ Erreur export TXT : {e}")

    def generer_rapport_fractal_unique(self) -> str:
        """Génère un rapport fractal unique et complet pour toutes les parties"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nom_fichier = f"rapport_fractal_complet_{timestamp}.txt"

            parties = self.obtenir_parties_disponibles()
            if not parties:
                print("❌ Aucune partie disponible")
                return ""

            rapport = []
            rapport.append("=" * 80)
            rapport.append("RAPPORT FRACTAL COMPLET - BACCARAT LUPASCO")
            rapport.append("=" * 80)
            rapport.append(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            rapport.append(f"Source : {self.fichier_json}")
            rapport.append(f"Nombre de parties analysées : {len(parties)}")
            rapport.append("")

            # Analyser chaque partie
            toutes_analyses = {}
            toutes_predictions = {}

            for partie_num in parties:
                print(f"🔬 Analyse fractale de la partie {partie_num}...")

                # Effectuer l'analyse une seule fois
                analyse = self.analyser_fractales_partie(partie_num)
                if analyse:
                    toutes_analyses[partie_num] = analyse

                    # Générer toutes les prédictions
                    predictions = {}
                    for methode in ["hurst", "dfa", "patterns", "multifractal", "combine"]:
                        pred = self.predire_prochaine_main_fractal(partie_num, methode)
                        predictions[methode] = pred
                    toutes_predictions[partie_num] = predictions

            # Section 1: Résumé Exécutif
            rapport.append("📊 RÉSUMÉ EXÉCUTIF")
            rapport.append("-" * 50)

            if self.statistiques_globales:
                stats = self.statistiques_globales
                rapport.append(f"Total parties : {stats.total_parties}")
                rapport.append(f"Total mains : {stats.total_mains}")
                rapport.append(f"Total manches P/B : {stats.total_manches_pb}")
                rapport.append(f"Total TIE : {stats.total_ties}")
                rapport.append(f"Moyenne mains/partie : {stats.total_mains/stats.total_parties:.1f}")

            rapport.append("")

            # Section 2: Analyse par Partie
            for partie_num in parties:
                if partie_num not in toutes_analyses:
                    continue

                analyse = toutes_analyses[partie_num]
                predictions = toutes_predictions[partie_num]
                index5_sequence = self.obtenir_index5_par_partie(partie_num)

                rapport.append(f"🎯 PARTIE {partie_num}")
                rapport.append("=" * 60)
                rapport.append(f"Nombre de mains : {len(index5_sequence)}")
                rapport.append("")

                # Caractéristiques fractales
                rapport.append("🔬 CARACTÉRISTIQUES FRACTALES")
                rapport.append("-" * 40)
                rapport.append(f"Dimension fractale : {analyse.dimension_fractale:.4f}")
                rapport.append(f"Exposant de Hurst : {analyse.exposant_hurst:.4f}")

                if analyse.exposant_hurst > 0.6:
                    rapport.append("  → Série PERSISTANTE (tendances long-terme)")
                elif analyse.exposant_hurst < 0.4:
                    rapport.append("  → Série ANTI-PERSISTANTE (retours à la moyenne)")
                else:
                    rapport.append("  → Série ALÉATOIRE (mouvement brownien)")

                rapport.append(f"Exposant DFA (α) : {analyse.dfa_alpha:.4f}")

                if analyse.dfa_alpha > 1.0:
                    rapport.append("  → Corrélations long-terme FORTES")
                elif analyse.dfa_alpha < 0.5:
                    rapport.append("  → Anti-corrélations présentes")
                else:
                    rapport.append("  → Comportement proche du bruit blanc")

                rapport.append("")

                # Entropie et complexité
                rapport.append("📊 ENTROPIE ET COMPLEXITÉ")
                rapport.append("-" * 40)

                # Comparaison des méthodes d'entropie de Shannon
                rapport.append("🔍 ENTROPIE DE SHANNON - COMPARAISON:")
                rapport.append(f"  • Observée : {analyse.entropie_shannon:.4f}")
                rapport.append(f"  • Théorique : {analyse.entropie_shannon_theorique:.4f}")
                rapport.append(f"  • Uniforme : {analyse.entropie_uniforme:.4f}")

                rapport.append("")
                rapport.append(f"Entropie de Rényi : {analyse.entropie_renyi:.4f}")
                rapport.append(f"Complexité Lempel-Ziv : {analyse.complexite_lempel_ziv:.4f}")

                # Interprétation de l'entropie
                max_entropy = analyse.entropie_shannon_theorique  # Utiliser l'entropie théorique comme référence
                entropy_ratio = analyse.entropie_shannon / max_entropy

                if entropy_ratio > 0.8:
                    rapport.append("  → Série très IMPRÉVISIBLE (haute entropie)")
                elif entropy_ratio > 0.6:
                    rapport.append("  → Série modérément prévisible")
                else:
                    rapport.append("  → Série avec patterns DÉTECTABLES (basse entropie)")

                rapport.append("")

                # Prédictions
                rapport.append("🎯 PRÉDICTIONS FRACTALES")
                rapport.append("-" * 40)

                for methode, pred in predictions.items():
                    if pred["prediction"]:
                        confiance_pct = pred["confiance"] * 100
                        rapport.append(f"{methode.upper():12} : {pred['prediction']} (confiance: {confiance_pct:.1f}%)")
                    else:
                        rapport.append(f"{methode.upper():12} : Prédiction impossible")

                # Recommandation
                best_pred = max(predictions.values(), key=lambda x: x["confiance"])
                if best_pred["prediction"] and best_pred["confiance"] > 0.5:
                    rapport.append("")
                    rapport.append("💡 RECOMMANDATION :")
                    rapport.append(f"Prédiction recommandée : {best_pred['prediction']}")
                    rapport.append(f"Méthode : {best_pred['methode']}")
                    rapport.append(f"Confiance : {best_pred['confiance']*100:.1f}%")

                rapport.append("")

                # Séquence INDEX5 complète
                rapport.append("📋 SÉQUENCE INDEX5 COMPLÈTE")
                rapport.append("-" * 40)

                for i, index5 in enumerate(index5_sequence, 1):
                    rapport.append(f"Main {i:2d}: {index5}")

                rapport.append("")
                rapport.append("=" * 60)
                rapport.append("")

            rapport.append("=" * 80)

            # Sauvegarder le rapport unique
            contenu_rapport = "\n".join(rapport)

            with open(nom_fichier, 'w', encoding='utf-8') as f:
                f.write(contenu_rapport)

            print(f"✅ Rapport fractal unique généré : {nom_fichier}")
            return nom_fichier

        except Exception as e:
            print(f"❌ Erreur génération rapport unique: {e}")
            traceback.print_exc()
            return ""

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                 SECTION 8 : EXEMPLES ET ANALYSES SPÉCIALISÉES               ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

# ─────────────────────────────────────────────────────────────────────────────
# 8.1 ANALYSES DE SÉQUENCES
# ─────────────────────────────────────────────────────────────────────────────

def analyse_sequences_index5(analyseur: AnalyseurBaccaratLupasco):
    """Exemple d'analyse : séquences d'INDEX5"""
    print(f"\n🔍 ANALYSE DES SÉQUENCES INDEX5")
    print(f"-" * 40)

    # Analyser les transitions les plus fréquentes
    transitions = {}

    for partie in analyseur.parties_analysees:
        mains = partie.mains
        for i in range(len(mains) - 1):
            current = mains[i].index5
            next_val = mains[i + 1].index5

            if current and next_val:
                transition = f"{current} → {next_val}"
                transitions[transition] = transitions.get(transition, 0) + 1

    # Top 10 transitions
    top_transitions = sorted(transitions.items(), key=lambda x: x[1], reverse=True)[:10]

    print(f"Top 10 transitions INDEX5 :")
    for i, (transition, count) in enumerate(top_transitions, 1):
        print(f"{i:2d}. {transition} : {count}")

def analyse_patterns_index1(analyseur: AnalyseurBaccaratLupasco):
    """Exemple d'analyse : patterns INDEX1"""
    print(f"\n🔄 ANALYSE DES PATTERNS INDEX1")
    print(f"-" * 40)

    # Analyser les séquences SYNC/DESYNC
    for partie in analyseur.parties_analysees:
        sequence_index1 = [main.index1 for main in partie.mains]

        # Compter les runs (séquences consécutives)
        runs = []
        current_run = 1

        for i in range(1, len(sequence_index1)):
            if sequence_index1[i] == sequence_index1[i-1]:
                current_run += 1
            else:
                runs.append((sequence_index1[i-1], current_run))
                current_run = 1

        if sequence_index1:
            runs.append((sequence_index1[-1], current_run))

        print(f"Partie {partie.partie_number} - Runs INDEX1 : {runs[:5]}...")  # Premiers 5 runs

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                   SECTION 9 : FONCTION PRINCIPALE ET TESTS                  ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

# ─────────────────────────────────────────────────────────────────────────────
# 9.1 FONCTION PRINCIPALE
# ─────────────────────────────────────────────────────────────────────────────

def main():
    """Fonction principale - exemple d'utilisation"""
    print("🎲 ANALYSEUR BACCARAT LUPASCO - PROGRAMME DE BASE")
    print("=" * 60)

    # Initialiser l'analyseur
    fichier = "dataset_baccarat_lupasco_20250629_144545.json"
    analyseur = AnalyseurBaccaratLupasco(fichier)

    # Charger les données
    if not analyseur.charger_donnees():
        print("❌ Impossible de charger les données")
        return

    # Exécuter l'analyse basique
    analyseur.executer_analyse_basique()

    # Exemples d'analyses spécialisées
    analyseur.executer_analyse_personnalisee(analyse_sequences_index5)
    analyseur.executer_analyse_personnalisee(analyse_patterns_index1)

    # Exemple d'utilisation des méthodes d'accès aux données
    print(f"\n🔍 EXEMPLES D'ACCÈS AUX DONNÉES :")
    print(f"-" * 40)

    # Obtenir tous les INDEX5
    tous_index5 = analyseur.obtenir_index5_toutes_mains()
    print(f"Total INDEX5 extraits : {len(tous_index5)}")
    print(f"Premiers INDEX5 : {[x[2] for x in tous_index5[:5]]}")

    # Filtrer des mains spécifiques
    mains_banker_sync = analyseur.filtrer_mains(index1=0, index3='BANKER')
    print(f"Mains BANKER en SYNC : {len(mains_banker_sync)}")

    # Export TXT
    analyseur.exporter_index5_txt("analyse_index5.txt")

    print(f"\n✅ Analyse terminée avec succès !")

# ─────────────────────────────────────────────────────────────────────────────
# 9.2 POINT D'ENTRÉE DU PROGRAMME
# ─────────────────────────────────────────────────────────────────────────────
if __name__ == "__main__":
    # Analyseur fractal avec rapport unique
    print("🚀 ANALYSEUR FRACTAL BACCARAT LUPASCO")
    print("=" * 50)

    # Utiliser la détection automatique du dataset le plus récent
    analyseur = AnalyseurBaccaratLupasco()

    if analyseur.charger_donnees():
        print("✅ Données chargées avec succès")

        # Afficher les informations de base
        analyseur.afficher_resume()

        # Obtenir les parties disponibles
        parties = analyseur.obtenir_parties_disponibles()
        print(f"\n📊 Parties disponibles: {parties}")

        if parties:
            # Générer le rapport fractal unique et complet
            print(f"\n🔬 GÉNÉRATION DU RAPPORT FRACTAL UNIQUE")
            print("-" * 50)

            rapport_file = analyseur.generer_rapport_fractal_unique()

            if rapport_file:
                print(f"✅ Rapport fractal unique généré: {rapport_file}")
                print(f"📄 Le rapport contient toutes les analyses fractales")
            else:
                print("❌ Erreur lors de la génération du rapport")
        else:
            print("❌ Aucune partie disponible pour l'analyse")

    else:
        print("❌ Erreur lors du chargement des données")
