#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR BACCARAT LUPASCO - BASE MODULAIRE
==========================================

Programme d'analyse modulaire et extensible pour les données de baccarat Lupasco.
Conçu pour être une base stable permettant l'implémentation de tout type d'analyse.

Fonctionnalités principales :
- Chargement et validation des données JSON
- Extraction des INDEX5 pour chaque main de chaque partie
- Architecture modulaire pour analyses spécialisées
- Interface simple et extensible
- Gestion d'erreurs robuste

Usage :
    analyseur = AnalyseurBaccaratLupasco("dataset_baccarat_lupasco_20250629_144545.json")
    analyseur.charger_donnees()
    analyseur.executer_analyse_basique()
"""

import json
import os
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import traceback

# ============================================================================
# STRUCTURES DE DONNÉES
# ============================================================================

@dataclass
class MainAnalysee:
    """Structure pour une main analysée avec toutes les informations essentielles"""
    partie_number: int
    main_number: int
    manche_pb_number: Optional[int]
    index1: int
    cards_count: int
    index2: str
    index3: str
    index5: str
    score_player: int
    score_banker: int
    timestamp: str

@dataclass
class PartieAnalysee:
    """Structure pour une partie analysée"""
    partie_number: int
    burn_cards_count: int
    initial_sync_state: int
    total_mains: int
    mains: List[MainAnalysee]
    
    # Statistiques calculées
    total_manches_pb: int = 0
    total_ties: int = 0
    
@dataclass
class StatistiquesGlobales:
    """Statistiques globales sur l'ensemble des données"""
    total_parties: int
    total_mains: int
    total_manches_pb: int
    total_ties: int
    
    # Répartition INDEX5
    repartition_index5: Dict[str, int]
    
    # Répartition par composants
    repartition_index1: Dict[int, int]
    repartition_index2: Dict[str, int]
    repartition_index3: Dict[str, int]

# ============================================================================
# CLASSE PRINCIPALE D'ANALYSE
# ============================================================================

class AnalyseurBaccaratLupasco:
    """
    Analyseur modulaire pour les données de baccarat Lupasco
    
    Cette classe sert de base stable pour tous types d'analyses.
    Elle charge les données, les structure et fournit une interface
    simple pour implémenter des analyses spécialisées.
    """
    
    def __init__(self, fichier_json: str):
        """
        Initialise l'analyseur
        
        Args:
            fichier_json: Chemin vers le fichier JSON de données
        """
        self.fichier_json = fichier_json
        self.donnees_brutes: Optional[Dict] = None
        self.parties_analysees: List[PartieAnalysee] = []
        self.statistiques_globales: Optional[StatistiquesGlobales] = None
        
        print(f"🔍 Analyseur Baccarat Lupasco initialisé")
        print(f"📁 Fichier cible : {fichier_json}")
    
    def charger_donnees(self) -> bool:
        """
        Charge et valide les données JSON
        
        Returns:
            bool: True si le chargement réussit, False sinon
        """
        try:
            print(f"\n📖 Chargement des données...")
            
            # Vérifier l'existence du fichier
            if not os.path.exists(self.fichier_json):
                print(f"❌ Erreur : Fichier {self.fichier_json} introuvable")
                return False
            
            # Charger le JSON
            with open(self.fichier_json, 'r', encoding='utf-8') as f:
                self.donnees_brutes = json.load(f)
            
            # Validation basique
            if not self._valider_structure_json():
                return False
            
            # Traitement des données
            if not self._traiter_donnees():
                return False
            
            # Calcul des statistiques
            self._calculer_statistiques_globales()
            
            print(f"✅ Données chargées avec succès !")
            print(f"📊 {len(self.parties_analysees)} parties analysées")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON : {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur inattendue : {e}")
            traceback.print_exc()
            return False
    
    def _valider_structure_json(self) -> bool:
        """Valide la structure du JSON chargé"""
        try:
            # Vérifier les clés principales
            if 'parties' not in self.donnees_brutes:
                print("❌ Erreur : Clé 'parties' manquante dans le JSON")
                return False
            
            if 'metadata' not in self.donnees_brutes:
                print("❌ Erreur : Clé 'metadata' manquante dans le JSON")
                return False
            
            parties = self.donnees_brutes['parties']
            if not isinstance(parties, list) or len(parties) == 0:
                print("❌ Erreur : Aucune partie trouvée dans les données")
                return False
            
            print(f"✅ Structure JSON valide - {len(parties)} parties détectées")
            return True
            
        except Exception as e:
            print(f"❌ Erreur validation : {e}")
            return False
    
    def _traiter_donnees(self) -> bool:
        """Traite les données brutes et les structure pour l'analyse"""
        try:
            print(f"🔄 Traitement des données...")
            
            parties_brutes = self.donnees_brutes['parties']
            
            for partie_data in parties_brutes:
                partie_analysee = self._traiter_partie(partie_data)
                if partie_analysee:
                    self.parties_analysees.append(partie_analysee)
            
            print(f"✅ {len(self.parties_analysees)} parties traitées")
            return True
            
        except Exception as e:
            print(f"❌ Erreur traitement : {e}")
            traceback.print_exc()
            return False
    
    def _traiter_partie(self, partie_data: Dict) -> Optional[PartieAnalysee]:
        """Traite une partie individuelle"""
        try:
            # Extraire les informations de base
            partie_number = partie_data.get('partie_number', 0)
            burn_info = partie_data.get('burn_info', {})
            burn_cards_count = burn_info.get('burn_cards_count', 0)
            initial_sync_state = burn_info.get('initial_sync_state', 0)
            
            # Traiter les mains
            mains_data = partie_data.get('mains', [])
            mains_analysees = []
            
            for main_data in mains_data:
                main_analysee = self._traiter_main(main_data, partie_number)
                if main_analysee:
                    mains_analysees.append(main_analysee)
            
            # Calculer les statistiques de la partie
            total_manches_pb = len([m for m in mains_analysees if m.index3 in ['PLAYER', 'BANKER']])
            total_ties = len([m for m in mains_analysees if m.index3 == 'TIE'])
            
            return PartieAnalysee(
                partie_number=partie_number,
                burn_cards_count=burn_cards_count,
                initial_sync_state=initial_sync_state,
                total_mains=len(mains_analysees),
                mains=mains_analysees,
                total_manches_pb=total_manches_pb,
                total_ties=total_ties
            )
            
        except Exception as e:
            print(f"❌ Erreur traitement partie {partie_data.get('partie_number', '?')} : {e}")
            return None
    
    def _traiter_main(self, main_data: Dict, partie_number: int) -> Optional[MainAnalysee]:
        """Traite une main individuelle"""
        try:
            # Ignorer les mains dummy (main_number = None)
            main_number = main_data.get('main_number')
            if main_number is None:
                return None
            
            return MainAnalysee(
                partie_number=partie_number,
                main_number=main_number,
                manche_pb_number=main_data.get('manche_pb_number'),
                index1=main_data.get('index1', 0),
                cards_count=main_data.get('cards_count', 0),
                index2=main_data.get('index2', ''),
                index3=main_data.get('index3', ''),
                index5=main_data.get('index5', ''),
                score_player=main_data.get('score_player', 0),
                score_banker=main_data.get('score_banker', 0),
                timestamp=main_data.get('timestamp', '')
            )
            
        except Exception as e:
            print(f"❌ Erreur traitement main : {e}")
            return None
    
    def _calculer_statistiques_globales(self):
        """Calcule les statistiques globales sur toutes les données"""
        try:
            # Compteurs globaux
            total_parties = len(self.parties_analysees)
            total_mains = sum(p.total_mains for p in self.parties_analysees)
            total_manches_pb = sum(p.total_manches_pb for p in self.parties_analysees)
            total_ties = sum(p.total_ties for p in self.parties_analysees)
            
            # Répartitions
            repartition_index5 = {}
            repartition_index1 = {0: 0, 1: 0}
            repartition_index2 = {'A': 0, 'B': 0, 'C': 0}
            repartition_index3 = {'PLAYER': 0, 'BANKER': 0, 'TIE': 0}
            
            # Parcourir toutes les mains
            for partie in self.parties_analysees:
                for main in partie.mains:
                    # INDEX5
                    if main.index5:
                        repartition_index5[main.index5] = repartition_index5.get(main.index5, 0) + 1
                    
                    # INDEX1
                    if main.index1 in repartition_index1:
                        repartition_index1[main.index1] += 1
                    
                    # INDEX2
                    if main.index2 in repartition_index2:
                        repartition_index2[main.index2] += 1
                    
                    # INDEX3
                    if main.index3 in repartition_index3:
                        repartition_index3[main.index3] += 1
            
            self.statistiques_globales = StatistiquesGlobales(
                total_parties=total_parties,
                total_mains=total_mains,
                total_manches_pb=total_manches_pb,
                total_ties=total_ties,
                repartition_index5=repartition_index5,
                repartition_index1=repartition_index1,
                repartition_index2=repartition_index2,
                repartition_index3=repartition_index3
            )
            
        except Exception as e:
            print(f"❌ Erreur calcul statistiques : {e}")
    
    def obtenir_index5_toutes_mains(self) -> List[Tuple[int, int, str]]:
        """
        Obtient la liste de tous les INDEX5 pour chaque main de chaque partie
        
        Returns:
            List[Tuple[int, int, str]]: Liste de (partie_number, main_number, index5)
        """
        index5_list = []
        
        for partie in self.parties_analysees:
            for main in partie.mains:
                index5_list.append((partie.partie_number, main.main_number, main.index5))
        
        return index5_list
    
    def obtenir_mains_par_partie(self, partie_number: int) -> List[MainAnalysee]:
        """
        Obtient toutes les mains d'une partie spécifique
        
        Args:
            partie_number: Numéro de la partie
            
        Returns:
            List[MainAnalysee]: Liste des mains de la partie
        """
        for partie in self.parties_analysees:
            if partie.partie_number == partie_number:
                return partie.mains
        return []
    
    def obtenir_index5_par_partie(self, partie_number: int) -> List[str]:
        """
        Obtient tous les INDEX5 d'une partie spécifique
        
        Args:
            partie_number: Numéro de la partie
            
        Returns:
            List[str]: Liste des INDEX5 de la partie
        """
        mains = self.obtenir_mains_par_partie(partie_number)
        return [main.index5 for main in mains if main.index5]
    
    def filtrer_mains(self, **criteres) -> List[MainAnalysee]:
        """
        Filtre les mains selon des critères spécifiques
        
        Args:
            **criteres: Critères de filtrage (index1, index2, index3, etc.)
            
        Returns:
            List[MainAnalysee]: Mains correspondant aux critères
        """
        mains_filtrees = []
        
        for partie in self.parties_analysees:
            for main in partie.mains:
                correspond = True
                
                for cle, valeur in criteres.items():
                    if hasattr(main, cle) and getattr(main, cle) != valeur:
                        correspond = False
                        break
                
                if correspond:
                    mains_filtrees.append(main)
        
        return mains_filtrees

    def executer_analyse_basique(self):
        """Exécute une analyse basique des données et affiche les résultats"""
        if not self.statistiques_globales:
            print("❌ Aucune donnée à analyser")
            return

        print(f"\n" + "="*60)
        print(f"📊 ANALYSE BASIQUE DES DONNÉES BACCARAT LUPASCO")
        print(f"="*60)

        stats = self.statistiques_globales

        # Statistiques générales
        print(f"\n📈 STATISTIQUES GÉNÉRALES :")
        print(f"Total parties : {stats.total_parties}")
        print(f"Total mains : {stats.total_mains}")
        print(f"Total manches P/B : {stats.total_manches_pb}")
        print(f"Total TIE : {stats.total_ties}")
        print(f"Moyenne mains/partie : {stats.total_mains/stats.total_parties:.1f}")

        # Répartition INDEX1
        print(f"\n🔄 RÉPARTITION INDEX1 (SYNC/DESYNC) :")
        total_index1 = sum(stats.repartition_index1.values())
        for index1, count in stats.repartition_index1.items():
            etat = "SYNC" if index1 == 0 else "DESYNC"
            pourcentage = (count/total_index1)*100 if total_index1 > 0 else 0
            print(f"{etat} ({index1}) : {count} ({pourcentage:.1f}%)")

        # Répartition INDEX2
        print(f"\n🎯 RÉPARTITION INDEX2 (CARTES) :")
        total_index2 = sum(stats.repartition_index2.values())
        for index2, count in stats.repartition_index2.items():
            cartes = {"A": "4 cartes", "B": "6 cartes", "C": "5 cartes"}.get(index2, index2)
            pourcentage = (count/total_index2)*100 if total_index2 > 0 else 0
            print(f"{index2} ({cartes}) : {count} ({pourcentage:.1f}%)")

        # Répartition INDEX3
        print(f"\n🏆 RÉPARTITION INDEX3 (RÉSULTATS) :")
        total_index3 = sum(stats.repartition_index3.values())
        for index3, count in stats.repartition_index3.items():
            pourcentage = (count/total_index3)*100 if total_index3 > 0 else 0
            print(f"{index3} : {count} ({pourcentage:.1f}%)")

        # Top 10 INDEX5
        print(f"\n🔝 TOP 10 INDEX5 LES PLUS FRÉQUENTS :")
        index5_tries = sorted(stats.repartition_index5.items(), key=lambda x: x[1], reverse=True)
        for i, (index5, count) in enumerate(index5_tries[:10], 1):
            pourcentage = (count/stats.total_mains)*100
            print(f"{i:2d}. {index5:12s} : {count:3d} ({pourcentage:.1f}%)")

        print(f"\n" + "="*60)

    def executer_analyse_personnalisee(self, fonction_analyse):
        """
        Exécute une fonction d'analyse personnalisée

        Args:
            fonction_analyse: Fonction qui prend l'analyseur en paramètre
        """
        try:
            print(f"\n🔬 Exécution d'une analyse personnalisée...")
            fonction_analyse(self)
        except Exception as e:
            print(f"❌ Erreur dans l'analyse personnalisée : {e}")
            traceback.print_exc()

    def exporter_index5_txt(self, nom_fichier: str = "index5_export.txt"):
        """
        Génère un fichier TXT vide avec en-tête minimal

        Args:
            nom_fichier: Nom du fichier TXT de sortie
        """
        try:
            with open(nom_fichier, 'w', encoding='utf-8') as txtfile:
                # En-tête minimal du fichier
                txtfile.write("EXPORT INDEX5 - ANALYSEUR BACCARAT LUPASCO\n")
                txtfile.write("=" * 60 + "\n")
                txtfile.write(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                txtfile.write(f"Source : {self.fichier_json}\n")
                txtfile.write("=" * 60 + "\n\n")

                # Le fichier reste vide de données comme demandé
                txtfile.write("# Fichier prêt pour analyses spécialisées\n")
                txtfile.write("# Aucune donnée affichée par défaut\n")

            print(f"✅ Export TXT terminé : {nom_fichier}")

        except Exception as e:
            print(f"❌ Erreur export TXT : {e}")

# ============================================================================
# EXEMPLES D'ANALYSES SPÉCIALISÉES
# ============================================================================

def analyse_sequences_index5(analyseur: AnalyseurBaccaratLupasco):
    """Exemple d'analyse : séquences d'INDEX5"""
    print(f"\n🔍 ANALYSE DES SÉQUENCES INDEX5")
    print(f"-" * 40)

    # Analyser les transitions les plus fréquentes
    transitions = {}

    for partie in analyseur.parties_analysees:
        mains = partie.mains
        for i in range(len(mains) - 1):
            current = mains[i].index5
            next_val = mains[i + 1].index5

            if current and next_val:
                transition = f"{current} → {next_val}"
                transitions[transition] = transitions.get(transition, 0) + 1

    # Top 10 transitions
    top_transitions = sorted(transitions.items(), key=lambda x: x[1], reverse=True)[:10]

    print(f"Top 10 transitions INDEX5 :")
    for i, (transition, count) in enumerate(top_transitions, 1):
        print(f"{i:2d}. {transition} : {count}")

def analyse_patterns_index1(analyseur: AnalyseurBaccaratLupasco):
    """Exemple d'analyse : patterns INDEX1"""
    print(f"\n🔄 ANALYSE DES PATTERNS INDEX1")
    print(f"-" * 40)

    # Analyser les séquences SYNC/DESYNC
    for partie in analyseur.parties_analysees:
        sequence_index1 = [main.index1 for main in partie.mains]

        # Compter les runs (séquences consécutives)
        runs = []
        current_run = 1

        for i in range(1, len(sequence_index1)):
            if sequence_index1[i] == sequence_index1[i-1]:
                current_run += 1
            else:
                runs.append((sequence_index1[i-1], current_run))
                current_run = 1

        if sequence_index1:
            runs.append((sequence_index1[-1], current_run))

        print(f"Partie {partie.partie_number} - Runs INDEX1 : {runs[:5]}...")  # Premiers 5 runs

# ============================================================================
# FONCTION PRINCIPALE ET TESTS
# ============================================================================

def main():
    """Fonction principale - exemple d'utilisation"""
    print("🎲 ANALYSEUR BACCARAT LUPASCO - PROGRAMME DE BASE")
    print("=" * 60)

    # Initialiser l'analyseur
    fichier = "dataset_baccarat_lupasco_20250629_144545.json"
    analyseur = AnalyseurBaccaratLupasco(fichier)

    # Charger les données
    if not analyseur.charger_donnees():
        print("❌ Impossible de charger les données")
        return

    # Exécuter l'analyse basique
    analyseur.executer_analyse_basique()

    # Exemples d'analyses spécialisées
    analyseur.executer_analyse_personnalisee(analyse_sequences_index5)
    analyseur.executer_analyse_personnalisee(analyse_patterns_index1)

    # Exemple d'utilisation des méthodes d'accès aux données
    print(f"\n🔍 EXEMPLES D'ACCÈS AUX DONNÉES :")
    print(f"-" * 40)

    # Obtenir tous les INDEX5
    tous_index5 = analyseur.obtenir_index5_toutes_mains()
    print(f"Total INDEX5 extraits : {len(tous_index5)}")
    print(f"Premiers INDEX5 : {[x[2] for x in tous_index5[:5]]}")

    # Filtrer des mains spécifiques
    mains_banker_sync = analyseur.filtrer_mains(index1=0, index3='BANKER')
    print(f"Mains BANKER en SYNC : {len(mains_banker_sync)}")

    # Export TXT
    analyseur.exporter_index5_txt("analyse_index5.txt")

    print(f"\n✅ Analyse terminée avec succès !")

if __name__ == "__main__":
    main()
